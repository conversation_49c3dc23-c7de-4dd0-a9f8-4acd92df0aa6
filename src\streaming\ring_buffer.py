"""
High-performance ring buffer implementation for audio streaming.
"""

import threading
import time
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List, Union
import numpy as np
from dataclasses import dataclass
from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector


@dataclass
class BufferStats:
    """Ring buffer statistics."""
    size: int
    capacity: int
    write_count: int
    read_count: int
    overruns: int
    underruns: int
    utilization: float
    latency_ms: float


class RingBuffer(LoggerMixin):
    """Thread-safe ring buffer with overflow/underflow detection."""
    
    def __init__(self, capacity: int, element_size: int = 1):
        """Initialize ring buffer.
        
        Args:
            capacity: Maximum number of elements
            element_size: Size of each element in bytes
        """
        super().__init__()
        self.capacity = capacity
        self.element_size = element_size
        
        # Buffer storage
        self._buffer = bytearray(capacity * element_size)
        
        # Pointers and counters
        self._write_pos = 0
        self._read_pos = 0
        self._size = 0
        self._write_count = 0
        self._read_count = 0
        self._overruns = 0
        self._underruns = 0
        
        # Threading primitives
        self._lock = threading.RLock()
        self._not_empty = threading.Condition(self._lock)
        self._not_full = threading.Condition(self._lock)
        
        # Timing
        self._last_write_time = time.time()
        
    def write(self, data: bytes, timeout: Optional[float] = None) -> int:
        """Write data to the buffer.
        
        Args:
            data: Data to write
            timeout: Optional timeout in seconds
            
        Returns:
            Number of bytes written
            
        Raises:
            TimeoutError: If timeout exceeded
        """
        if not data:
            return 0
            
        data_len = len(data)
        elements_to_write = (data_len + self.element_size - 1) // self.element_size
        
        with self._not_full:
            # Wait for space if needed
            if timeout is not None:
                deadline = time.time() + timeout
                
            while self._size + elements_to_write > self.capacity:
                if timeout is not None:
                    remaining = deadline - time.time()
                    if remaining <= 0:
                        raise TimeoutError("Write timeout exceeded")
                    if not self._not_full.wait(remaining):
                        raise TimeoutError("Write timeout exceeded")
                else:
                    # Overwrite oldest data if no timeout
                    overflow = self._size + elements_to_write - self.capacity
                    self._advance_read_pos(overflow)
                    self._overruns += 1
                    self.logger.warning("Buffer overrun", overruns=self._overruns)
                    break
            
            # Write data
            bytes_written = 0
            remaining_data = data
            
            while remaining_data and self._size < self.capacity:
                # Calculate contiguous space to end of buffer
                available_to_end = self.capacity - self._write_pos
                chunk_elements = min(
                    available_to_end,
                    len(remaining_data) // self.element_size,
                    self.capacity - self._size
                )
                
                if chunk_elements == 0:
                    break
                    
                chunk_bytes = chunk_elements * self.element_size
                start_idx = self._write_pos * self.element_size
                end_idx = start_idx + chunk_bytes
                
                self._buffer[start_idx:end_idx] = remaining_data[:chunk_bytes]
                
                self._write_pos = (self._write_pos + chunk_elements) % self.capacity
                self._size += chunk_elements
                bytes_written += chunk_bytes
                remaining_data = remaining_data[chunk_bytes:]
                
            self._write_count += bytes_written
            self._last_write_time = time.time()
            
            # Update metrics
            metrics_collector.set_gauge("buffer_size", self._size * self.element_size)
            
            # Notify readers
            self._not_empty.notify_all()
            
        return bytes_written
    
    def read(self, max_elements: int, timeout: Optional[float] = None) -> bytes:
        """Read data from the buffer.
        
        Args:
            max_elements: Maximum number of elements to read
            timeout: Optional timeout in seconds
            
        Returns:
            Read data
            
        Raises:
            TimeoutError: If timeout exceeded
        """
        with self._not_empty:
            # Wait for data if needed
            if timeout is not None:
                deadline = time.time() + timeout
                
                while self._size == 0:
                    remaining = deadline - time.time()
                    if remaining <= 0:
                        self._underruns += 1
                        raise TimeoutError("Read timeout exceeded")
                    if not self._not_empty.wait(remaining):
                        self._underruns += 1
                        raise TimeoutError("Read timeout exceeded")
            elif self._size == 0:
                self._underruns += 1
                return b''
            
            # Read data
            elements_to_read = min(max_elements, self._size)
            result = bytearray()
            
            remaining_elements = elements_to_read
            while remaining_elements > 0:
                # Calculate contiguous data to end of buffer
                available_to_end = self.capacity - self._read_pos
                chunk_elements = min(available_to_end, remaining_elements)
                
                start_idx = self._read_pos * self.element_size
                end_idx = start_idx + (chunk_elements * self.element_size)
                
                result.extend(self._buffer[start_idx:end_idx])
                
                self._read_pos = (self._read_pos + chunk_elements) % self.capacity
                self._size -= chunk_elements
                remaining_elements -= chunk_elements
                
            self._read_count += len(result)
            
            # Update metrics
            metrics_collector.set_gauge("buffer_size", self._size * self.element_size)
            
            # Notify writers
            self._not_full.notify_all()
            
        return bytes(result)
    
    def peek(self, max_elements: int) -> bytes:
        """Peek at data without removing it from buffer.
        
        Args:
            max_elements: Maximum number of elements to peek
            
        Returns:
            Peeked data
        """
        with self._lock:
            if self._size == 0:
                return b''
                
            elements_to_peek = min(max_elements, self._size)
            result = bytearray()
            
            read_pos = self._read_pos
            remaining_elements = elements_to_peek
            
            while remaining_elements > 0:
                available_to_end = self.capacity - read_pos
                chunk_elements = min(available_to_end, remaining_elements)
                
                start_idx = read_pos * self.element_size
                end_idx = start_idx + (chunk_elements * self.element_size)
                
                result.extend(self._buffer[start_idx:end_idx])
                
                read_pos = (read_pos + chunk_elements) % self.capacity
                remaining_elements -= chunk_elements
                
        return bytes(result)
    
    def _advance_read_pos(self, elements: int):
        """Advance read position (internal use only).
        
        Args:
            elements: Number of elements to advance
        """
        elements = min(elements, self._size)
        self._read_pos = (self._read_pos + elements) % self.capacity
        self._size -= elements
        self._read_count += elements * self.element_size
    
    def clear(self):
        """Clear all data from the buffer."""
        with self._lock:
            self._read_pos = 0
            self._write_pos = 0
            self._size = 0
            
            # Notify all waiters
            self._not_empty.notify_all()
            self._not_full.notify_all()
    
    def get_stats(self) -> BufferStats:
        """Get buffer statistics.
        
        Returns:
            Buffer statistics
        """
        with self._lock:
            utilization = self._size / self.capacity if self.capacity > 0 else 0.0
            
            # Estimate latency based on write rate
            current_time = time.time()
            time_since_write = current_time - self._last_write_time
            latency_ms = time_since_write * 1000.0
            
            return BufferStats(
                size=self._size,
                capacity=self.capacity,
                write_count=self._write_count,
                read_count=self._read_count,
                overruns=self._overruns,
                underruns=self._underruns,
                utilization=utilization,
                latency_ms=latency_ms
            )
    
    @property
    def size(self) -> int:
        """Current buffer size in elements."""
        with self._lock:
            return self._size
    
    @property
    def free_space(self) -> int:
        """Available space in elements."""
        with self._lock:
            return self.capacity - self._size
    
    @property
    def is_empty(self) -> bool:
        """Check if buffer is empty."""
        with self._lock:
            return self._size == 0
    
    @property
    def is_full(self) -> bool:
        """Check if buffer is full."""
        with self._lock:
            return self._size >= self.capacity


class AudioRingBuffer(RingBuffer):
    """Specialized ring buffer for audio data with sample-aware operations."""
    
    def __init__(self, capacity_samples: int, sample_rate: int, channels: int = 1, 
                 dtype: np.dtype = np.float32):
        """Initialize audio ring buffer.
        
        Args:
            capacity_samples: Buffer capacity in audio samples
            sample_rate: Audio sample rate
            channels: Number of audio channels
            dtype: Audio data type
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.dtype = dtype
        self.bytes_per_sample = np.dtype(dtype).itemsize * channels
        
        super().__init__(capacity_samples, self.bytes_per_sample)
        
        self.logger.info(
            "Audio buffer initialized",
            capacity_samples=capacity_samples,
            sample_rate=sample_rate,
            channels=channels,
            dtype=str(dtype),
            bytes_per_sample=self.bytes_per_sample
        )
    
    def write_audio(self, audio_data: np.ndarray, timeout: Optional[float] = None) -> int:
        """Write audio samples to the buffer.
        
        Args:
            audio_data: Audio data as numpy array
            timeout: Optional timeout in seconds
            
        Returns:
            Number of samples written
        """
        if audio_data.dtype != self.dtype:
            audio_data = audio_data.astype(self.dtype)
            
        # Ensure correct shape
        if audio_data.ndim == 1 and self.channels > 1:
            audio_data = audio_data.reshape(-1, self.channels)
        elif audio_data.ndim == 2 and self.channels == 1:
            audio_data = audio_data.flatten()
            
        # Convert to bytes
        audio_bytes = audio_data.tobytes()
        bytes_written = self.write(audio_bytes, timeout)
        samples_written = bytes_written // self.bytes_per_sample
        
        return samples_written
    
    def read_audio(self, max_samples: int, timeout: Optional[float] = None) -> np.ndarray:
        """Read audio samples from the buffer.
        
        Args:
            max_samples: Maximum number of samples to read
            timeout: Optional timeout in seconds
            
        Returns:
            Audio data as numpy array
        """
        audio_bytes = self.read(max_samples, timeout)
        
        if not audio_bytes:
            return np.array([], dtype=self.dtype)
            
        # Convert to numpy array
        audio_data = np.frombuffer(audio_bytes, dtype=self.dtype)
        
        # Reshape for multi-channel
        if self.channels > 1:
            samples = len(audio_data) // self.channels
            audio_data = audio_data.reshape(samples, self.channels)
            
        return audio_data
    
    def peek_audio(self, max_samples: int) -> np.ndarray:
        """Peek at audio samples without removing them.
        
        Args:
            max_samples: Maximum number of samples to peek
            
        Returns:
            Audio data as numpy array
        """
        audio_bytes = self.peek(max_samples)
        
        if not audio_bytes:
            return np.array([], dtype=self.dtype)
            
        audio_data = np.frombuffer(audio_bytes, dtype=self.dtype)
        
        if self.channels > 1:
            samples = len(audio_data) // self.channels
            audio_data = audio_data.reshape(samples, self.channels)
            
        return audio_data
    
    def get_duration_ms(self) -> float:
        """Get current buffer duration in milliseconds.
        
        Returns:
            Buffer duration in milliseconds
        """
        with self._lock:
            samples = self._size
            return (samples / self.sample_rate) * 1000.0
    
    def get_capacity_ms(self) -> float:
        """Get buffer capacity in milliseconds.
        
        Returns:
            Buffer capacity in milliseconds
        """
        return (self.capacity / self.sample_rate) * 1000.0
    
    def has_minimum_audio(self, min_duration_ms: float) -> bool:
        """Check if buffer has minimum audio duration.
        
        Args:
            min_duration_ms: Minimum duration in milliseconds
            
        Returns:
            True if buffer has enough audio
        """
        required_samples = int((min_duration_ms / 1000.0) * self.sample_rate)
        return self.size >= required_samples
    
    def read_duration(self, duration_ms: float, timeout: Optional[float] = None) -> np.ndarray:
        """Read specific duration of audio.
        
        Args:
            duration_ms: Duration to read in milliseconds
            timeout: Optional timeout in seconds
            
        Returns:
            Audio data for the requested duration
        """
        samples_needed = int((duration_ms / 1000.0) * self.sample_rate)
        return self.read_audio(samples_needed, timeout)