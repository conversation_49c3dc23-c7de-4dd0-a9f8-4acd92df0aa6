#!/usr/bin/env python3
"""
Simple test script to verify the speech-to-text system works.
This bypasses the OODA loop and focuses on core functionality.
"""

import asyncio
import time
import numpy as np
from src.models.distil_whisper_model import DistilWhisperModel
from src.audio.capture import AudioCapturer, AudioCapturerConfig
from src.streaming.stream_processor import StreamProcessor

async def simple_test():
    """Run a simple test of the core components."""
    print("🎤 Starting Simple Speech-to-Text Test")
    
    # 1. Test Distil-Whisper model loading
    print("\n📦 Loading Distil-Whisper model...")
    model = DistilWhisperModel(
        model_size="distil-small.en",
        device="cpu",
        torch_dtype="float16"
    )
    
    try:
        await model.load()
        print(f"✅ Model loaded successfully! Memory: {model.get_memory_usage_mb():.1f}MB")
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return
    
    # 2. Test audio capture
    print("\n🎧 Setting up audio capture...")
    audio_config = AudioCapturerConfig(
        sample_rate=16000,
        channels=1,
        chunk_size=1024,
        buffer_duration_ms=1000,
        preferred_backend="sounddevice"
    )
    
    capturer = AudioCapturer(audio_config)
    
    # 3. Simple transcription test
    print("\n🗣️  Starting 10-second audio capture and transcription test...")
    print("   Say something into your microphone!")
    
    try:
        await capturer.start_capture()
        print("   Recording... (10 seconds)")
        
        # Capture for 10 seconds
        start_time = time.time()
        while time.time() - start_time < 10:
            await asyncio.sleep(0.1)
            
            # Get audio data from buffer
            if capturer.audio_buffer.size > 16000:  # 1 second of audio
                audio_data = capturer.audio_buffer.read(16000)
                
                if np.max(np.abs(audio_data)) > 0.01:  # Check if there's actual audio
                    print(f"   🎵 Processing audio chunk ({len(audio_data)} samples)...")
                    
                    # Transcribe
                    result = await model.transcribe(audio_data, 16000)
                    if result.text.strip():
                        print(f"   📝 Transcription: '{result.text}' (confidence: {result.confidence:.2f})")
        
        await capturer.stop_capture()
        print("✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Audio capture failed: {e}")
        await capturer.stop_capture()
    
    # 4. Cleanup
    await model.unload()
    print("\n🧹 Cleanup completed")

if __name__ == "__main__":
    asyncio.run(simple_test())
