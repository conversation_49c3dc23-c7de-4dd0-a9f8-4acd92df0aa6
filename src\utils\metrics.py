"""
Performance metrics collection and monitoring.
"""

import time
from collections import defaultdict, deque
from contextlib import contextmanager
from dataclasses import dataclass, field
from threading import Lock
from typing import Dict, List, Optional, Any, Callable
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import asyncio


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    value: float
    timestamp: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class LatencyStats:
    """Latency statistics."""
    count: int = 0
    total_ms: float = 0.0
    min_ms: float = float('inf')
    max_ms: float = 0.0
    p50_ms: float = 0.0
    p95_ms: float = 0.0
    p99_ms: float = 0.0


class MetricsCollector:
    """Collect and track performance metrics."""
    
    def __init__(self, max_history: int = 10000):
        """Initialize metrics collector.
        
        Args:
            max_history: Maximum number of metrics to keep in memory
        """
        self.max_history = max_history
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self._latencies: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = defaultdict(float)
        self._lock = Lock()
        
        # Prometheus metrics
        self._setup_prometheus_metrics()
        
    def _setup_prometheus_metrics(self):
        """Setup Prometheus metrics."""
        self.audio_latency = Histogram(
            'audio_latency_seconds',
            'Audio processing latency',
            buckets=(0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0)
        )
        
        self.transcription_latency = Histogram(
            'transcription_latency_seconds',
            'Transcription processing latency',
            buckets=(0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0, 10.0)
        )
        
        self.buffer_size = Gauge(
            'audio_buffer_size_bytes',
            'Current audio buffer size'
        )
        
        self.active_streams = Gauge(
            'active_audio_streams',
            'Number of active audio streams'
        )
        
        self.model_switches = Counter(
            'model_switches_total',
            'Total number of model switches'
        )
        
        self.errors_total = Counter(
            'errors_total',
            'Total number of errors',
            ['error_type', 'component']
        )
        
        self.transcription_accuracy = Gauge(
            'transcription_accuracy_ratio',
            'Estimated transcription accuracy'
        )
        
    def record_latency(self, operation: str, latency_ms: float, labels: Optional[Dict[str, str]] = None):
        """Record a latency measurement.
        
        Args:
            operation: Name of the operation
            latency_ms: Latency in milliseconds
            labels: Optional labels for the metric
        """
        with self._lock:
            self._latencies[operation].append(latency_ms)
            
            # Update Prometheus metrics
            if operation == "audio_processing":
                self.audio_latency.observe(latency_ms / 1000.0)
            elif operation == "transcription":
                self.transcription_latency.observe(latency_ms / 1000.0)
                
        # Store detailed metric
        metric = PerformanceMetric(
            name=f"{operation}_latency",
            value=latency_ms,
            timestamp=time.time(),
            labels=labels or {}
        )
        
        with self._lock:
            self._metrics[f"{operation}_latency"].append(metric)
    
    def increment_counter(self, name: str, value: int = 1, labels: Optional[Dict[str, str]] = None):
        """Increment a counter metric.
        
        Args:
            name: Counter name
            value: Value to increment by
            labels: Optional labels
        """
        with self._lock:
            self._counters[name] += value
            
        # Update Prometheus counter
        if name == "model_switches":
            self.model_switches.inc(value)
        elif name.startswith("errors"):
            error_type = labels.get("error_type", "unknown") if labels else "unknown"
            component = labels.get("component", "unknown") if labels else "unknown"
            self.errors_total.labels(error_type=error_type, component=component).inc(value)
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Set a gauge metric value.
        
        Args:
            name: Gauge name
            value: Value to set
            labels: Optional labels
        """
        with self._lock:
            self._gauges[name] = value
            
        # Update Prometheus gauge
        if name == "buffer_size":
            self.buffer_size.set(value)
        elif name == "active_streams":
            self.active_streams.set(value)
        elif name == "transcription_accuracy":
            self.transcription_accuracy.set(value)
    
    def get_latency_stats(self, operation: str) -> LatencyStats:
        """Get latency statistics for an operation.
        
        Args:
            operation: Operation name
            
        Returns:
            Latency statistics
        """
        with self._lock:
            latencies = list(self._latencies[operation])
            
        if not latencies:
            return LatencyStats()
            
        latencies.sort()
        count = len(latencies)
        
        return LatencyStats(
            count=count,
            total_ms=sum(latencies),
            min_ms=min(latencies),
            max_ms=max(latencies),
            p50_ms=latencies[int(count * 0.5)] if count > 0 else 0.0,
            p95_ms=latencies[int(count * 0.95)] if count > 0 else 0.0,
            p99_ms=latencies[int(count * 0.99)] if count > 0 else 0.0
        )
    
    def get_counter_value(self, name: str) -> int:
        """Get current counter value.
        
        Args:
            name: Counter name
            
        Returns:
            Counter value
        """
        with self._lock:
            return self._counters[name]
    
    def get_gauge_value(self, name: str) -> float:
        """Get current gauge value.
        
        Args:
            name: Gauge name
            
        Returns:
            Gauge value
        """
        with self._lock:
            return self._gauges[name]
    
    def get_recent_metrics(self, operation: str, limit: int = 100) -> List[PerformanceMetric]:
        """Get recent metrics for an operation.
        
        Args:
            operation: Operation name
            limit: Maximum number of metrics to return
            
        Returns:
            List of recent metrics
        """
        with self._lock:
            metrics = list(self._metrics[operation])
            
        return metrics[-limit:] if limit > 0 else metrics
    
    def clear_metrics(self, operation: Optional[str] = None):
        """Clear metrics for an operation or all operations.
        
        Args:
            operation: Operation name, or None to clear all
        """
        with self._lock:
            if operation:
                self._metrics[operation].clear()
                self._latencies[operation].clear()
            else:
                self._metrics.clear()
                self._latencies.clear()
                self._counters.clear()
                self._gauges.clear()
    
    @contextmanager
    def measure_latency(self, operation: str, labels: Optional[Dict[str, str]] = None):
        """Context manager to measure operation latency.
        
        Args:
            operation: Operation name
            labels: Optional labels
            
        Yields:
            None
            
        Example:
            with metrics.measure_latency("transcription"):
                result = transcribe_audio(audio_data)
        """
        start_time = time.perf_counter()
        try:
            yield
        finally:
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000.0
            self.record_latency(operation, latency_ms, labels)
    
    async def measure_async_latency(self, operation: str, coro: Callable, 
                                  labels: Optional[Dict[str, str]] = None):
        """Measure latency of an async operation.
        
        Args:
            operation: Operation name
            coro: Coroutine to measure
            labels: Optional labels
            
        Returns:
            Result of the coroutine
        """
        start_time = time.perf_counter()
        try:
            result = await coro
            return result
        finally:
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000.0
            self.record_latency(operation, latency_ms, labels)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics.
        
        Returns:
            Dictionary containing metrics summary
        """
        with self._lock:
            latency_summaries = {}
            for operation in self._latencies:
                latency_summaries[operation] = self.get_latency_stats(operation).__dict__
                
            return {
                "latencies": latency_summaries,
                "counters": dict(self._counters),
                "gauges": dict(self._gauges),
                "timestamp": time.time()
            }
    
    def start_prometheus_server(self, port: int = 8080):
        """Start Prometheus metrics server.
        
        Args:
            port: Port to serve metrics on
        """
        try:
            start_http_server(port)
        except OSError as e:
            # Port might already be in use
            pass


# Global metrics collector instance
metrics_collector = MetricsCollector()