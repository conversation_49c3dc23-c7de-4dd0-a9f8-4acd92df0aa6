"""
Audio chunk management for optimized real-time processing.
"""

import time
from collections import deque
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Tuple
import numpy as np

from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector


@dataclass
class AudioChunk:
    """Audio chunk with metadata."""
    data: np.ndarray
    timestamp: float
    chunk_id: int
    sample_rate: int
    duration_ms: float = 0.0
    confidence: float = 0.0
    is_processed: bool = False
    processing_start_time: Optional[float] = None
    processing_end_time: Optional[float] = None
    overlap_start_samples: int = 0
    overlap_end_samples: int = 0
    
    def __post_init__(self):
        if self.duration_ms == 0.0:
            self.duration_ms = (len(self.data) / self.sample_rate) * 1000.0


@dataclass
class ChunkBuffer:
    """Buffer for managing audio chunks."""
    chunks: deque = field(default_factory=deque)
    max_size: int = 10
    total_samples: int = 0
    total_duration_ms: float = 0.0
    
    def add_chunk(self, chunk: AudioChunk):
        """Add chunk to buffer."""
        if len(self.chunks) >= self.max_size:
            removed = self.chunks.popleft()
            self.total_samples -= len(removed.data)
            self.total_duration_ms -= removed.duration_ms
        
        self.chunks.append(chunk)
        self.total_samples += len(chunk.data)
        self.total_duration_ms += chunk.duration_ms
    
    def get_chunk(self, chunk_id: int) -> Optional[AudioChunk]:
        """Get chunk by ID."""
        for chunk in self.chunks:
            if chunk.chunk_id == chunk_id:
                return chunk
        return None
    
    def get_latest_chunk(self) -> Optional[AudioChunk]:
        """Get most recent chunk."""
        return self.chunks[-1] if self.chunks else None
    
    def clear(self):
        """Clear all chunks."""
        self.chunks.clear()
        self.total_samples = 0
        self.total_duration_ms = 0.0


class ChunkManager(LoggerMixin):
    """Manages audio chunks for optimal real-time processing."""
    
    def __init__(self,
                 chunk_duration_ms: int = 100,
                 overlap_duration_ms: int = 50,
                 max_buffer_size: int = 20,
                 quality_threshold: float = 0.7):
        """Initialize chunk manager.
        
        Args:
            chunk_duration_ms: Base chunk duration in milliseconds
            overlap_duration_ms: Overlap between chunks in milliseconds
            max_buffer_size: Maximum number of chunks to buffer
            quality_threshold: Minimum quality threshold for chunks
        """
        super().__init__()
        
        self.chunk_duration_ms = chunk_duration_ms
        self.overlap_duration_ms = overlap_duration_ms
        self.max_buffer_size = max_buffer_size
        self.quality_threshold = quality_threshold
        
        # Chunk storage
        self._chunk_buffer = ChunkBuffer(max_size=max_buffer_size)
        self._processed_chunks: Dict[int, AudioChunk] = {}
        self._chunk_counter = 0
        
        # Overlap management
        self._overlap_buffer: Optional[np.ndarray] = None
        self._last_chunk_end_time = 0.0
        
        # Performance tracking
        self._chunk_stats = {
            "total_created": 0,
            "total_processed": 0,
            "total_dropped": 0,
            "avg_processing_time_ms": 0.0,
            "quality_failures": 0
        }
        
        # Adaptive parameters
        self._adaptive_enabled = True
        self._recent_processing_times: deque = deque(maxlen=50)
        self._target_processing_time_ms = 100.0
        
    def create_chunk(self, 
                    audio_data: np.ndarray, 
                    sample_rate: int,
                    timestamp: Optional[float] = None) -> AudioChunk:
        """Create an audio chunk with optimal parameters.
        
        Args:
            audio_data: Audio samples
            sample_rate: Sample rate
            timestamp: Optional timestamp (uses current time if None)
            
        Returns:
            Created audio chunk
        """
        if timestamp is None:
            timestamp = time.time()
        
        # Calculate overlap samples
        overlap_samples = int((self.overlap_duration_ms / 1000.0) * sample_rate)
        
        # Apply overlap from previous chunk
        overlap_start_samples = 0
        if self._overlap_buffer is not None and len(self._overlap_buffer) > 0:
            # Prepend overlap from previous chunk
            audio_data = np.concatenate([self._overlap_buffer, audio_data])
            overlap_start_samples = len(self._overlap_buffer)
        
        # Extract overlap for next chunk
        overlap_end_samples = min(overlap_samples, len(audio_data))
        if overlap_end_samples > 0:
            self._overlap_buffer = audio_data[-overlap_end_samples:].copy()
        else:
            self._overlap_buffer = None
        
        # Create chunk
        chunk = AudioChunk(
            data=audio_data.copy(),
            timestamp=timestamp,
            chunk_id=self._chunk_counter,
            sample_rate=sample_rate,
            overlap_start_samples=overlap_start_samples,
            overlap_end_samples=overlap_end_samples
        )
        
        self._chunk_counter += 1
        self._chunk_stats["total_created"] += 1
        
        # Add to buffer
        self._chunk_buffer.add_chunk(chunk)
        
        self.logger.debug("Created audio chunk",
                         chunk_id=chunk.chunk_id,
                         duration_ms=chunk.duration_ms,
                         samples=len(chunk.data),
                         overlap_start=overlap_start_samples,
                         overlap_end=overlap_end_samples)
        
        return chunk
    
    def create_optimal_chunks(self, 
                            audio_data: np.ndarray, 
                            sample_rate: int,
                            base_timestamp: Optional[float] = None) -> List[AudioChunk]:
        """Create multiple optimally-sized chunks from audio data.
        
        Args:
            audio_data: Audio samples
            sample_rate: Sample rate
            base_timestamp: Base timestamp for chunks
            
        Returns:
            List of created chunks
        """
        if base_timestamp is None:
            base_timestamp = time.time()
        
        chunks = []
        
        # Calculate optimal chunk size
        chunk_size_samples = self._calculate_optimal_chunk_size(sample_rate)
        overlap_samples = int((self.overlap_duration_ms / 1000.0) * sample_rate)
        
        # Calculate step size (chunk size minus overlap)
        step_size = max(1, chunk_size_samples - overlap_samples)
        
        # Create chunks with overlap
        start_idx = 0
        chunk_index = 0
        
        while start_idx < len(audio_data):
            end_idx = min(start_idx + chunk_size_samples, len(audio_data))
            
            if end_idx - start_idx < chunk_size_samples // 2:
                # Skip chunks that are too small
                break
            
            chunk_data = audio_data[start_idx:end_idx]
            chunk_timestamp = base_timestamp + (start_idx / sample_rate)
            
            chunk = AudioChunk(
                data=chunk_data.copy(),
                timestamp=chunk_timestamp,
                chunk_id=self._chunk_counter + chunk_index,
                sample_rate=sample_rate,
                overlap_start_samples=overlap_samples if start_idx > 0 else 0,
                overlap_end_samples=overlap_samples if end_idx < len(audio_data) else 0
            )
            
            chunks.append(chunk)
            self._chunk_buffer.add_chunk(chunk)
            
            start_idx += step_size
            chunk_index += 1
        
        self._chunk_counter += len(chunks)
        self._chunk_stats["total_created"] += len(chunks)
        
        self.logger.debug("Created optimal chunks",
                         total_chunks=len(chunks),
                         chunk_size_samples=chunk_size_samples,
                         overlap_samples=overlap_samples,
                         step_size=step_size)
        
        return chunks
    
    def _calculate_optimal_chunk_size(self, sample_rate: int) -> int:
        """Calculate optimal chunk size based on current performance.
        
        Args:
            sample_rate: Audio sample rate
            
        Returns:
            Optimal chunk size in samples
        """
        # Base chunk size
        base_size_samples = int((self.chunk_duration_ms / 1000.0) * sample_rate)
        
        if not self._adaptive_enabled or len(self._recent_processing_times) < 5:
            return base_size_samples
        
        # Calculate average recent processing time
        avg_processing_time = sum(self._recent_processing_times) / len(self._recent_processing_times)
        
        # Adaptive adjustment
        if avg_processing_time > self._target_processing_time_ms * 1.2:
            # Processing is slow, reduce chunk size
            adjustment_factor = 0.9
        elif avg_processing_time < self._target_processing_time_ms * 0.8:
            # Processing is fast, can increase chunk size
            adjustment_factor = 1.1
        else:
            # Processing time is acceptable
            adjustment_factor = 1.0
        
        adjusted_size = int(base_size_samples * adjustment_factor)
        
        # Apply limits
        min_size = int((50 / 1000.0) * sample_rate)  # 50ms minimum
        max_size = int((500 / 1000.0) * sample_rate)  # 500ms maximum
        
        return max(min_size, min(adjusted_size, max_size))
    
    def mark_chunk_processed(self, 
                           chunk_id: int, 
                           processing_time_ms: float,
                           quality_score: float = 1.0):
        """Mark a chunk as processed and update statistics.
        
        Args:
            chunk_id: ID of processed chunk
            processing_time_ms: Time taken to process
            quality_score: Quality score (0.0-1.0)
        """
        chunk = self._chunk_buffer.get_chunk(chunk_id)
        if chunk:
            chunk.is_processed = True
            chunk.processing_end_time = time.time()
            chunk.confidence = quality_score
            
            # Store processed chunk
            self._processed_chunks[chunk_id] = chunk
            
            # Update statistics
            self._chunk_stats["total_processed"] += 1
            self._recent_processing_times.append(processing_time_ms)
            
            # Update average processing time
            total_processed = self._chunk_stats["total_processed"]
            if total_processed == 1:
                self._chunk_stats["avg_processing_time_ms"] = processing_time_ms
            else:
                # Exponential moving average
                alpha = 0.1
                self._chunk_stats["avg_processing_time_ms"] = (
                    alpha * processing_time_ms + 
                    (1 - alpha) * self._chunk_stats["avg_processing_time_ms"]
                )
            
            # Track quality failures
            if quality_score < self.quality_threshold:
                self._chunk_stats["quality_failures"] += 1
                self.logger.warning("Low quality chunk",
                                  chunk_id=chunk_id,
                                  quality_score=quality_score,
                                  threshold=self.quality_threshold)
            
            self.logger.debug("Marked chunk processed",
                            chunk_id=chunk_id,
                            processing_time_ms=processing_time_ms,
                            quality_score=quality_score)
            
            # Update metrics
            metrics_collector.record_latency("chunk_processing", processing_time_ms)
            metrics_collector.set_gauge("chunk_quality", quality_score)
        
        # Clean up old processed chunks
        self._cleanup_old_chunks()
    
    def _cleanup_old_chunks(self):
        """Clean up old processed chunks to free memory."""
        current_time = time.time()
        max_age_seconds = 60.0  # Keep chunks for 1 minute
        
        chunks_to_remove = []
        for chunk_id, chunk in self._processed_chunks.items():
            if chunk.processing_end_time and (current_time - chunk.processing_end_time) > max_age_seconds:
                chunks_to_remove.append(chunk_id)
        
        for chunk_id in chunks_to_remove:
            del self._processed_chunks[chunk_id]
    
    def get_chunk_quality_stats(self) -> Dict[str, float]:
        """Get chunk quality statistics.
        
        Returns:
            Dictionary of quality statistics
        """
        if not self._processed_chunks:
            return {"avg_quality": 0.0, "min_quality": 0.0, "max_quality": 0.0}
        
        qualities = [chunk.confidence for chunk in self._processed_chunks.values()]
        
        return {
            "avg_quality": sum(qualities) / len(qualities),
            "min_quality": min(qualities),
            "max_quality": max(qualities),
            "quality_failures": self._chunk_stats["quality_failures"],
            "quality_failure_rate": self._chunk_stats["quality_failures"] / max(1, self._chunk_stats["total_processed"])
        }
    
    def optimize_for_latency(self, target_latency_ms: float):
        """Optimize chunk parameters for target latency.
        
        Args:
            target_latency_ms: Target latency in milliseconds
        """
        self._target_processing_time_ms = target_latency_ms * 0.8  # 80% of target for safety
        
        # Adjust chunk duration if needed
        if self._chunk_stats["avg_processing_time_ms"] > target_latency_ms:
            # Reduce chunk duration
            new_duration = max(50, self.chunk_duration_ms * 0.9)
            self.logger.info("Reducing chunk duration for latency",
                           old_duration=self.chunk_duration_ms,
                           new_duration=new_duration,
                           target_latency=target_latency_ms)
            self.chunk_duration_ms = new_duration
        
        self.logger.info("Optimized for latency", target_latency_ms=target_latency_ms)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get chunk manager statistics.
        
        Returns:
            Dictionary of statistics
        """
        quality_stats = self.get_chunk_quality_stats()
        
        recent_times = list(self._recent_processing_times)
        avg_recent_time = sum(recent_times) / len(recent_times) if recent_times else 0.0
        
        return {
            "chunk_duration_ms": self.chunk_duration_ms,
            "overlap_duration_ms": self.overlap_duration_ms,
            "max_buffer_size": self.max_buffer_size,
            "quality_threshold": self.quality_threshold,
            "buffer_stats": {
                "current_chunks": len(self._chunk_buffer.chunks),
                "total_samples": self._chunk_buffer.total_samples,
                "total_duration_ms": self._chunk_buffer.total_duration_ms
            },
            "processing_stats": {
                "total_created": self._chunk_stats["total_created"],
                "total_processed": self._chunk_stats["total_processed"],
                "total_dropped": self._chunk_stats["total_dropped"],
                "avg_processing_time_ms": self._chunk_stats["avg_processing_time_ms"],
                "recent_avg_processing_time_ms": avg_recent_time,
                "target_processing_time_ms": self._target_processing_time_ms
            },
            "quality_stats": quality_stats,
            "adaptive_enabled": self._adaptive_enabled,
            "overlap_buffer_size": len(self._overlap_buffer) if self._overlap_buffer is not None else 0
        }
    
    def reset_stats(self):
        """Reset all statistics."""
        self._chunk_stats = {
            "total_created": 0,
            "total_processed": 0,
            "total_dropped": 0,
            "avg_processing_time_ms": 0.0,
            "quality_failures": 0
        }
        self._recent_processing_times.clear()
        self._processed_chunks.clear()
        self.logger.info("Reset chunk manager statistics")
    
    def set_adaptive_enabled(self, enabled: bool):
        """Enable or disable adaptive chunk sizing.
        
        Args:
            enabled: Whether to enable adaptive sizing
        """
        self._adaptive_enabled = enabled
        self.logger.info("Adaptive chunk sizing", enabled=enabled)
    
    def clear_buffers(self):
        """Clear all buffers and reset state."""
        self._chunk_buffer.clear()
        self._processed_chunks.clear()
        self._overlap_buffer = None
        self._last_chunk_end_time = 0.0
        self.logger.info("Cleared all chunk buffers")