# Core async framework
asyncio-utils>=0.1.4

# Audio processing and capture
pyaudio>=0.2.11
sounddevice>=0.4.6
numpy>=1.24.0
scipy>=1.10.0

# Speech recognition models
faster-whisper>=0.9.0
transformers>=4.30.0
torch>=2.0.0
torchaudio>=2.0.0

# Cross-platform audio backends
pulsectl>=22.3.2; sys_platform=="linux"
pywin32>=306; sys_platform=="win32"

# Configuration and utilities
pydantic>=2.0.0
pydantic-settings>=2.0.0
toml>=0.10.2
click>=8.1.0

# Logging and monitoring
structlog>=23.1.0
prometheus-client>=0.17.0

# Performance optimizations
uvloop>=0.17.0; sys_platform!="win32"
numba>=0.57.0

# Development and testing (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0