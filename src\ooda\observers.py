"""
OODA loop observers for monitoring system state.
"""

import time
import psutil
from typing import Dict, Any, Optional
from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector
from .controller import OODAState


class BaseObserver(LoggerMixin):
    """Base class for OODA observers."""
    
    def __init__(self, name: str):
        """Initialize observer.
        
        Args:
            name: Observer name
        """
        super().__init__()
        self.name = name
        self._last_observation_time = 0
        
    async def observe(self, state: OODAState) -> Dict[str, Any]:
        """Perform observation.
        
        Args:
            state: Current OODA state
            
        Returns:
            Observation data
        """
        start_time = time.time()
        
        try:
            observations = await self._do_observe(state)
            self._last_observation_time = start_time
            return observations
        except Exception as e:
            self.logger.error("Observation error", observer=self.name, error=str(e))
            return {}
    
    async def _do_observe(self, state: OODAState) -> Dict[str, Any]:
        """Implement specific observation logic.
        
        Args:
            state: Current OODA state
            
        Returns:
            Observation data
        """
        raise NotImplementedError


class AudioObserver(BaseObserver):
    """Observer for audio system state."""
    
    def __init__(self, audio_capturer=None):
        """Initialize audio observer.
        
        Args:
            audio_capturer: Audio capturer instance to monitor
        """
        super().__init__("audio")
        self.audio_capturer = audio_capturer
        
    async def _do_observe(self, state: OODAState) -> Dict[str, Any]:
        """Observe audio system state."""
        observations = {
            "timestamp": time.time(),
            "audio_available": False,
            "buffer_level": 0.0,
            "latency_ms": 0.0,
            "is_capturing": False,
            "device_name": None,
            "sample_rate": 0,
            "channels": 0
        }
        
        if self.audio_capturer:
            try:
                stats = self.audio_capturer.get_capture_stats()
                
                observations.update({
                    "audio_available": self.audio_capturer.has_audio_data(100),  # 100ms minimum
                    "buffer_level": stats["buffer_stats"]["utilization"],
                    "latency_ms": stats["current_latency_ms"],
                    "is_capturing": stats["is_capturing"],
                    "device_name": stats["device_name"],
                    "sample_rate": stats["sample_rate"],
                    "channels": stats["channels"],
                    "callback_count": stats["callback_count"],
                    "total_samples": stats["total_samples"],
                    "avg_callback_rate_hz": stats["avg_callback_rate_hz"],
                    "buffer_overruns": stats["buffer_stats"]["overruns"],
                    "buffer_underruns": stats["buffer_stats"]["underruns"]
                })
                
            except Exception as e:
                self.logger.error("Error getting audio stats", error=str(e))
        
        return observations
    
    def set_audio_capturer(self, capturer):
        """Set the audio capturer to monitor.
        
        Args:
            capturer: Audio capturer instance
        """
        self.audio_capturer = capturer


class PerformanceObserver(BaseObserver):
    """Observer for system performance metrics."""
    
    def __init__(self):
        """Initialize performance observer."""
        super().__init__("performance")
        self._last_cpu_percent = 0
        self._last_memory_percent = 0
        
    async def _do_observe(self, state: OODAState) -> Dict[str, Any]:
        """Observe system performance."""
        try:
            # Get CPU and memory usage
            cpu_percent = psutil.cpu_percent(interval=None)  # Non-blocking
            memory = psutil.virtual_memory()
            
            # Get process-specific metrics
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            
            observations = {
                "timestamp": time.time(),
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_mb": memory.available / (1024 * 1024),
                    "memory_used_mb": memory.used / (1024 * 1024),
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                "process": {
                    "cpu_percent": process_cpu,
                    "memory_rss_mb": process_memory.rss / (1024 * 1024),
                    "memory_vms_mb": process_memory.vms / (1024 * 1024),
                    "num_threads": process.num_threads(),
                    "num_fds": process.num_fds() if hasattr(process, 'num_fds') else None
                },
                "thresholds": {
                    "cpu_high": cpu_percent > 80,
                    "memory_high": memory.percent > 85,
                    "process_cpu_high": process_cpu > 50,
                    "process_memory_high": process_memory.rss > 500 * 1024 * 1024  # 500MB
                }
            }
            
            # Store for trend analysis
            self._last_cpu_percent = cpu_percent
            self._last_memory_percent = memory.percent
            
            return observations
            
        except Exception as e:
            self.logger.error("Error collecting performance metrics", error=str(e))
            return {"timestamp": time.time(), "error": str(e)}


class SystemObserver(BaseObserver):
    """Observer for general system state."""
    
    def __init__(self):
        """Initialize system observer."""
        super().__init__("system")
        self._startup_time = time.time()
        
    async def _do_observe(self, state: OODAState) -> Dict[str, Any]:
        """Observe system state."""
        current_time = time.time()
        
        observations = {
            "timestamp": current_time,
            "uptime_seconds": current_time - self._startup_time,
            "ooda": {
                "cycle_count": state.cycle_count,
                "current_phase": state.phase.value,
                "last_cycle_time_ms": state.last_cycle_time_ms,
                "observations_count": len(state.observations),
                "decisions_count": len(state.decisions),
                "actions_count": len(state.actions_taken)
            },
            "metrics": {
                "buffer_size": metrics_collector.get_gauge_value("buffer_size"),
                "active_streams": metrics_collector.get_gauge_value("active_streams"),
                "model_switches": metrics_collector.get_counter_value("model_switches"),
                "errors_total": metrics_collector.get_counter_value("errors")
            }
        }
        
        # Add latency statistics
        try:
            audio_latency_stats = metrics_collector.get_latency_stats("audio_processing")
            transcription_latency_stats = metrics_collector.get_latency_stats("transcription")
            ooda_cycle_stats = metrics_collector.get_latency_stats("ooda_cycle")
            
            observations["latency_stats"] = {
                "audio_processing": {
                    "count": audio_latency_stats.count,
                    "avg_ms": audio_latency_stats.total_ms / max(audio_latency_stats.count, 1),
                    "min_ms": audio_latency_stats.min_ms if audio_latency_stats.count > 0 else 0,
                    "max_ms": audio_latency_stats.max_ms,
                    "p95_ms": audio_latency_stats.p95_ms
                },
                "transcription": {
                    "count": transcription_latency_stats.count,
                    "avg_ms": transcription_latency_stats.total_ms / max(transcription_latency_stats.count, 1),
                    "min_ms": transcription_latency_stats.min_ms if transcription_latency_stats.count > 0 else 0,
                    "max_ms": transcription_latency_stats.max_ms,
                    "p95_ms": transcription_latency_stats.p95_ms
                },
                "ooda_cycle": {
                    "count": ooda_cycle_stats.count,
                    "avg_ms": ooda_cycle_stats.total_ms / max(ooda_cycle_stats.count, 1),
                    "min_ms": ooda_cycle_stats.min_ms if ooda_cycle_stats.count > 0 else 0,
                    "max_ms": ooda_cycle_stats.max_ms,
                    "p95_ms": ooda_cycle_stats.p95_ms
                }
            }
            
        except Exception as e:
            self.logger.error("Error collecting latency stats", error=str(e))
        
        return observations


class ModelObserver(BaseObserver):
    """Observer for ML model state and performance."""
    
    def __init__(self, model_pool=None):
        """Initialize model observer.
        
        Args:
            model_pool: Model pool instance to monitor
        """
        super().__init__("model")
        self.model_pool = model_pool
        
    async def _do_observe(self, state: OODAState) -> Dict[str, Any]:
        """Observe model state."""
        observations = {
            "timestamp": time.time(),
            "models_available": False,
            "active_model": None,
            "model_count": 0,
            "warm_models": 0,
            "loading_models": 0
        }
        
        if self.model_pool:
            try:
                pool_stats = self.model_pool.get_stats()
                
                observations.update({
                    "models_available": pool_stats["available_models"] > 0,
                    "active_model": pool_stats.get("current_model"),
                    "model_count": pool_stats["total_models"],
                    "warm_models": pool_stats["available_models"],
                    "loading_models": pool_stats.get("loading_models", 0),
                    "model_performance": pool_stats.get("performance_metrics", {}),
                    "memory_usage_mb": pool_stats.get("memory_usage_mb", 0),
                    "avg_inference_time_ms": pool_stats.get("avg_inference_time_ms", 0)
                })
                
            except Exception as e:
                self.logger.error("Error getting model pool stats", error=str(e))
        
        return observations
    
    def set_model_pool(self, pool):
        """Set the model pool to monitor.
        
        Args:
            pool: Model pool instance
        """
        self.model_pool = pool


class TranscriptionObserver(BaseObserver):
    """Observer for transcription pipeline state."""
    
    def __init__(self, transcription_pipeline=None):
        """Initialize transcription observer.
        
        Args:
            transcription_pipeline: Transcription pipeline to monitor
        """
        super().__init__("transcription")
        self.pipeline = transcription_pipeline
        
    async def _do_observe(self, state: OODAState) -> Dict[str, Any]:
        """Observe transcription pipeline state."""
        observations = {
            "timestamp": time.time(),
            "pipeline_active": False,
            "queue_length": 0,
            "processing_count": 0,
            "recent_transcriptions": 0,
            "accuracy_estimate": 0.0,
            "avg_processing_time_ms": 0.0
        }
        
        if self.pipeline:
            try:
                pipeline_stats = self.pipeline.get_stats()
                
                observations.update({
                    "pipeline_active": pipeline_stats["is_active"],
                    "queue_length": pipeline_stats["queue_length"],
                    "processing_count": pipeline_stats["processing_count"],
                    "recent_transcriptions": pipeline_stats.get("recent_transcriptions", 0),
                    "accuracy_estimate": pipeline_stats.get("accuracy_estimate", 0.0),
                    "avg_processing_time_ms": pipeline_stats.get("avg_processing_time_ms", 0.0),
                    "total_processed": pipeline_stats.get("total_processed", 0),
                    "success_rate": pipeline_stats.get("success_rate", 0.0),
                    "error_rate": pipeline_stats.get("error_rate", 0.0)
                })
                
            except Exception as e:
                self.logger.error("Error getting transcription pipeline stats", error=str(e))
        
        return observations
    
    def set_pipeline(self, pipeline):
        """Set the transcription pipeline to monitor.
        
        Args:
            pipeline: Transcription pipeline instance
        """
        self.pipeline = pipeline