"""
Faster-Whisper model implementation for high-performance transcription.
"""

import asyncio
import gc
import time
from typing import Dict, Any, List, Optional
import numpy as np

from .base import BaseModel, ModelType, ModelState, TranscriptionResult, ModelLoadError, ModelTranscriptionError


class FasterWhisperModel(BaseModel):
    """Faster-Whisper model implementation."""
    
    def __init__(self, 
                 model_size: str = "base",
                 device: str = "auto",
                 compute_type: str = "float16",
                 cpu_threads: int = 0):
        """Initialize Faster-Whisper model.
        
        Args:
            model_size: Model size (tiny, base, small, medium, large-v2, large-v3)
            device: Device to run on ("cpu", "cuda", "auto")
            compute_type: Compute type ("float16", "float32", "int8")
            cpu_threads: Number of CPU threads (0 for auto)
        """
        super().__init__(f"faster-whisper-{model_size}", ModelType.FASTER_WHISPER)
        
        self.model_size = model_size
        self.device = device
        self.compute_type = compute_type
        self.cpu_threads = cpu_threads
        
        # Model configuration
        self._supported_languages = [
            "en", "zh", "de", "es", "ru", "ko", "fr", "ja", "pt", "tr", "pl", "ca", "nl",
            "ar", "sv", "it", "id", "hi", "fi", "vi", "he", "uk", "el", "ms", "cs", "ro",
            "da", "hu", "ta", "no", "th", "ur", "hr", "bg", "lt", "la", "mi", "ml", "cy",
            "sk", "te", "fa", "lv", "bn", "sr", "az", "sl", "kn", "et", "mk", "br", "eu",
            "is", "hy", "ne", "mn", "bs", "kk", "sq", "sw", "gl", "mr", "pa", "si", "km",
            "sn", "yo", "so", "af", "oc", "ka", "be", "tg", "sd", "gu", "am", "yi", "lo",
            "uz", "fo", "ht", "ps", "tk", "nn", "mt", "sa", "lb", "my", "bo", "tl", "mg",
            "as", "tt", "haw", "ln", "ha", "ba", "jw", "su"
        ]
        
    async def load(self, **kwargs) -> None:
        """Load the Faster-Whisper model."""
        if self.state == ModelState.LOADED:
            return
        
        if self.state == ModelState.LOADING:
            # Wait for loading to complete
            while self.state == ModelState.LOADING:
                await asyncio.sleep(0.1)
            return
        
        self.state = ModelState.LOADING
        load_start_time = time.time()
        
        try:
            self.logger.info(
                "Loading Faster-Whisper model",
                model_size=self.model_size,
                device=self.device,
                compute_type=self.compute_type
            )
            
            # Import faster-whisper in a thread to avoid blocking
            await asyncio.get_event_loop().run_in_executor(
                None, self._load_model_sync
            )
            
            self._load_time = time.time() - load_start_time
            self.state = ModelState.LOADED
            
            # Update memory usage
            self.metrics.memory_usage_mb = self.get_memory_usage_mb()
            
            self.logger.info(
                "Faster-Whisper model loaded",
                model_size=self.model_size,
                load_time_s=self._load_time,
                memory_mb=self.metrics.memory_usage_mb
            )
            
        except Exception as e:
            self.state = ModelState.ERROR
            self.logger.error("Failed to load Faster-Whisper model", error=str(e))
            raise ModelLoadError(f"Failed to load Faster-Whisper model: {e}")
    
    def _load_model_sync(self):
        """Load model synchronously (runs in thread)."""
        try:
            from faster_whisper import WhisperModel
            
            # Determine device
            if self.device == "auto":
                try:
                    import torch
                    device = "cuda" if torch.cuda.is_available() else "cpu"
                except ImportError:
                    device = "cpu"
            else:
                device = self.device
            
            # Load model
            self._model = WhisperModel(
                self.model_size,
                device=device,
                compute_type=self.compute_type,
                cpu_threads=self.cpu_threads if self.cpu_threads > 0 else None,
                download_root=None,  # Use default cache directory
                local_files_only=False
            )
            
            self.device = device  # Update with actual device used
            
        except ImportError as e:
            raise ModelLoadError("faster-whisper not installed. Install with: pip install faster-whisper")
        except Exception as e:
            raise ModelLoadError(f"Failed to load model: {e}")
    
    async def unload(self) -> None:
        """Unload the model to free memory."""
        if self.state == ModelState.UNLOADED:
            return
        
        self.logger.info("Unloading Faster-Whisper model", model=self.model_name)
        
        try:
            # Delete model
            if self._model is not None:
                del self._model
                self._model = None
            
            # Force garbage collection
            gc.collect()
            
            # If using CUDA, clear cache
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            
            self.state = ModelState.UNLOADED
            self.metrics.memory_usage_mb = 0.0
            
            self.logger.info("Faster-Whisper model unloaded")
            
        except Exception as e:
            self.logger.error("Error unloading model", error=str(e))
    
    async def transcribe(self, 
                        audio_data: np.ndarray, 
                        sample_rate: int = 16000,
                        language: Optional[str] = None,
                        task: str = "transcribe",
                        beam_size: int = 5,
                        best_of: int = 5,
                        patience: float = 1.0,
                        temperature: float = 0.0,
                        word_timestamps: bool = False,
                        **kwargs) -> TranscriptionResult:
        """Transcribe audio using Faster-Whisper.
        
        Args:
            audio_data: Audio samples as numpy array
            sample_rate: Audio sample rate
            language: Language code (auto-detect if None)
            task: Task type ("transcribe" or "translate")
            beam_size: Beam search size
            best_of: Number of candidates to consider
            patience: Patience factor for beam search
            temperature: Sampling temperature
            word_timestamps: Whether to return word-level timestamps
            **kwargs: Additional parameters
            
        Returns:
            Transcription result
        """
        if not self.is_loaded():
            raise ModelTranscriptionError("Model is not loaded")
        
        try:
            # Ensure audio is float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Resample if needed (faster-whisper expects 16kHz)
            if sample_rate != 16000:
                audio_data = await self._resample_audio(audio_data, sample_rate, 16000)
            
            # Normalize audio
            if len(audio_data) > 0:
                max_val = np.max(np.abs(audio_data))
                if max_val > 0:
                    audio_data = audio_data / max_val
            
            # Run transcription in executor to avoid blocking
            result = await asyncio.get_event_loop().run_in_executor(
                None, self._transcribe_sync, audio_data, language, task, 
                beam_size, best_of, patience, temperature, word_timestamps
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Transcription error", error=str(e))
            raise ModelTranscriptionError(f"Transcription failed: {e}")
    
    def _transcribe_sync(self, 
                        audio_data: np.ndarray,
                        language: Optional[str],
                        task: str,
                        beam_size: int,
                        best_of: int,
                        patience: float,
                        temperature: float,
                        word_timestamps: bool) -> TranscriptionResult:
        """Synchronous transcription (runs in thread)."""
        try:
            # Transcribe using faster-whisper
            segments, info = self._model.transcribe(
                audio_data,
                language=language,
                task=task,
                beam_size=beam_size,
                best_of=best_of,
                patience=patience,
                temperature=temperature,
                word_timestamps=word_timestamps,
                vad_filter=True,  # Enable VAD filtering
                vad_parameters=dict(min_silence_duration_ms=500)
            )
            
            # Collect segments
            segment_list = []
            word_list = []
            full_text = ""
            total_confidence = 0.0
            segment_count = 0
            
            for segment in segments:
                segment_dict = {
                    "start": segment.start,
                    "end": segment.end,
                    "text": segment.text.strip(),
                    "avg_logprob": segment.avg_logprob,
                    "no_speech_prob": segment.no_speech_prob
                }
                
                segment_list.append(segment_dict)
                full_text += segment.text
                
                # Calculate confidence from log probability
                confidence = np.exp(segment.avg_logprob)
                total_confidence += confidence
                segment_count += 1
                
                # Add word timestamps if available
                if word_timestamps and hasattr(segment, 'words'):
                    for word in segment.words:
                        word_dict = {
                            "start": word.start,
                            "end": word.end,
                            "word": word.word,
                            "probability": word.probability
                        }
                        word_list.append(word_dict)
            
            # Calculate overall confidence
            avg_confidence = total_confidence / segment_count if segment_count > 0 else 0.0
            
            # Detect language if not specified
            detected_language = info.language if hasattr(info, 'language') else language
            
            return TranscriptionResult(
                text=full_text.strip(),
                confidence=avg_confidence,
                processing_time_ms=0.0,  # Will be set by caller
                language=detected_language,
                segments=segment_list,
                word_timestamps=word_list
            )
            
        except Exception as e:
            raise ModelTranscriptionError(f"Faster-Whisper transcription failed: {e}")
    
    async def _resample_audio(self, audio_data: np.ndarray, 
                            from_rate: int, to_rate: int) -> np.ndarray:
        """Resample audio data.
        
        Args:
            audio_data: Input audio samples
            from_rate: Source sample rate
            to_rate: Target sample rate
            
        Returns:
            Resampled audio data
        """
        if from_rate == to_rate:
            return audio_data
        
        try:
            # Use scipy for resampling if available
            from scipy.signal import resample
            
            # Calculate new length
            new_length = int(len(audio_data) * to_rate / from_rate)
            
            # Resample
            resampled = resample(audio_data, new_length)
            return resampled.astype(np.float32)
            
        except ImportError:
            # Fallback to simple linear interpolation
            ratio = to_rate / from_rate
            new_length = int(len(audio_data) * ratio)
            
            # Simple linear interpolation
            old_indices = np.linspace(0, len(audio_data) - 1, new_length)
            new_audio = np.interp(old_indices, np.arange(len(audio_data)), audio_data)
            
            return new_audio.astype(np.float32)
    
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        if not self.is_loaded():
            return 0.0
        
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert to MB
            
        except ImportError:
            # Fallback estimate based on model size
            size_estimates = {
                "tiny": 39,
                "base": 74,
                "small": 244,
                "medium": 769,
                "large-v2": 1550,
                "large-v3": 1550,
                "large": 1550
            }
            return size_estimates.get(self.model_size, 100)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return self._supported_languages.copy()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed model information.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_type": self.model_type.value,
            "model_size": self.model_size,
            "device": self.device,
            "compute_type": self.compute_type,
            "cpu_threads": self.cpu_threads,
            "supported_languages": len(self._supported_languages),
            "supports_word_timestamps": True,
            "supports_language_detection": True,
            "supports_translation": True,
            "supports_vad": True
        }