"""
Adaptation engine for executing OODA decisions.
"""

import asyncio
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, List, Optional, Callable
from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector
from .controller import OODAState
from .decision_engine import Decision, DecisionType


class AdaptationStrategy(Enum):
    """Adaptation strategies."""
    IMMEDIATE = "immediate"        # Execute immediately
    GRADUAL = "gradual"           # Execute gradually over time
    SCHEDULED = "scheduled"       # Execute at scheduled time
    CONDITIONAL = "conditional"   # Execute when conditions are met


@dataclass
class AdaptationResult:
    """Result of an adaptation action."""
    decision: Decision
    success: bool
    execution_time_ms: float
    error_message: Optional[str] = None
    side_effects: List[str] = None
    
    def __post_init__(self):
        if self.side_effects is None:
            self.side_effects = []


class AdaptationEngine(LoggerMixin):
    """Engine for executing adaptive decisions."""
    
    def __init__(self):
        """Initialize adaptation engine."""
        super().__init__()
        self._action_handlers: Dict[DecisionType, Callable] = {}
        self._adaptation_history: List[AdaptationResult] = []
        self._max_history = 1000
        
        # Component references (set by main system)
        self.audio_capturer = None
        self.model_pool = None
        self.transcription_pipeline = None
        self.ooda_controller = None
        
        # Register default action handlers
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default action handlers."""
        # Audio-related actions
        self.register_handler(DecisionType.ADJUST_BUFFER_SIZE, self._handle_adjust_buffer_size)
        self.register_handler(DecisionType.CHANGE_AUDIO_DEVICE, self._handle_change_audio_device)
        self.register_handler(DecisionType.ADJUST_SAMPLE_RATE, self._handle_adjust_sample_rate)
        self.register_handler(DecisionType.ENABLE_NOISE_GATE, self._handle_enable_noise_gate)
        
        # Model-related actions
        self.register_handler(DecisionType.SWITCH_MODEL, self._handle_switch_model)
        self.register_handler(DecisionType.ADJUST_MODEL_PARAMS, self._handle_adjust_model_params)
        self.register_handler(DecisionType.PRELOAD_MODEL, self._handle_preload_model)
        self.register_handler(DecisionType.UNLOAD_MODEL, self._handle_unload_model)
        
        # Performance-related actions
        self.register_handler(DecisionType.ADJUST_CHUNK_SIZE, self._handle_adjust_chunk_size)
        self.register_handler(DecisionType.THROTTLE_PROCESSING, self._handle_throttle_processing)
        self.register_handler(DecisionType.INCREASE_WORKERS, self._handle_increase_workers)
        self.register_handler(DecisionType.DECREASE_WORKERS, self._handle_decrease_workers)
        
        # System-related actions
        self.register_handler(DecisionType.RESTART_COMPONENT, self._handle_restart_component)
        self.register_handler(DecisionType.ENABLE_FALLBACK, self._handle_enable_fallback)
        self.register_handler(DecisionType.ADJUST_INTERVALS, self._handle_adjust_intervals)
        self.register_handler(DecisionType.CLEAR_BUFFERS, self._handle_clear_buffers)
    
    def register_handler(self, decision_type: DecisionType, handler: Callable):
        """Register an action handler for a decision type.
        
        Args:
            decision_type: Type of decision
            handler: Async function to handle the decision
        """
        self._action_handlers[decision_type] = handler
        self.logger.debug("Registered action handler", decision_type=decision_type.value)
    
    def set_components(self, **components):
        """Set component references.
        
        Args:
            **components: Component instances (audio_capturer, model_pool, etc.)
        """
        for name, component in components.items():
            if hasattr(self, name):
                setattr(self, name, component)
                self.logger.debug("Set component reference", component=name)
    
    async def execute_decisions(self, decisions: List[Decision]) -> List[AdaptationResult]:
        """Execute a list of decisions.
        
        Args:
            decisions: List of decisions to execute
            
        Returns:
            List of adaptation results
        """
        results = []
        
        for decision in decisions:
            result = await self._execute_single_decision(decision)
            results.append(result)
            
            # Store in history
            self._adaptation_history.append(result)
            if len(self._adaptation_history) > self._max_history:
                self._adaptation_history = self._adaptation_history[-self._max_history:]
            
            # Log result
            if result.success:
                self.logger.info("Adaptation successful", 
                               decision_type=decision.type.value,
                               execution_time_ms=result.execution_time_ms)
            else:
                self.logger.error("Adaptation failed",
                                decision_type=decision.type.value,
                                error=result.error_message)
        
        return results
    
    async def _execute_single_decision(self, decision: Decision) -> AdaptationResult:
        """Execute a single decision.
        
        Args:
            decision: Decision to execute
            
        Returns:
            Adaptation result
        """
        start_time = time.time()
        
        try:
            handler = self._action_handlers.get(decision.type)
            if not handler:
                return AdaptationResult(
                    decision=decision,
                    success=False,
                    execution_time_ms=0,
                    error_message=f"No handler for decision type: {decision.type.value}"
                )
            
            # Execute the handler
            side_effects = await handler(decision)
            
            execution_time_ms = (time.time() - start_time) * 1000.0
            
            # Record metrics
            metrics_collector.record_latency("adaptation_execution", execution_time_ms)
            metrics_collector.increment_counter("adaptations_executed")
            
            return AdaptationResult(
                decision=decision,
                success=True,
                execution_time_ms=execution_time_ms,
                side_effects=side_effects or []
            )
            
        except Exception as e:
            execution_time_ms = (time.time() - start_time) * 1000.0
            
            metrics_collector.increment_counter(
                "errors",
                labels={"error_type": "adaptation", "component": "engine"}
            )
            
            return AdaptationResult(
                decision=decision,
                success=False,
                execution_time_ms=execution_time_ms,
                error_message=str(e)
            )
    
    # Audio-related action handlers
    
    async def _handle_adjust_buffer_size(self, decision: Decision) -> List[str]:
        """Handle buffer size adjustment."""
        if not self.audio_capturer:
            raise RuntimeError("Audio capturer not available")
        
        params = decision.parameters
        action = params.get("action", "adjust")
        factor = params.get("factor", 1.0)
        
        # Get current buffer size
        current_capacity = self.audio_capturer.audio_buffer.capacity
        
        if action == "increase":
            new_capacity = int(current_capacity * factor)
        elif action == "decrease":
            new_capacity = int(current_capacity * factor)
        else:
            new_capacity = params.get("size", current_capacity)
        
        # Apply limits
        min_capacity = 1024
        max_capacity = 16384
        new_capacity = max(min_capacity, min(new_capacity, max_capacity))
        
        # This would require recreating the buffer or implementing dynamic resizing
        # For now, log the intended change
        self.logger.info("Buffer size adjustment",
                        current=current_capacity,
                        target=new_capacity,
                        action=action,
                        factor=factor)
        
        return [f"Buffer size adjusted from {current_capacity} to {new_capacity}"]
    
    async def _handle_change_audio_device(self, decision: Decision) -> List[str]:
        """Handle audio device change."""
        if not self.audio_capturer:
            raise RuntimeError("Audio capturer not available")
        
        params = decision.parameters
        device_name = params.get("device_name")
        
        # This would require stopping current capture and starting with new device
        self.logger.info("Audio device change requested", target_device=device_name)
        
        return [f"Audio device change to {device_name} (not fully implemented)"]
    
    async def _handle_adjust_sample_rate(self, decision: Decision) -> List[str]:
        """Handle sample rate adjustment."""
        params = decision.parameters
        new_rate = params.get("sample_rate", 16000)
        
        self.logger.info("Sample rate adjustment", target_rate=new_rate)
        
        return [f"Sample rate adjustment to {new_rate}Hz (requires restart)"]
    
    async def _handle_enable_noise_gate(self, decision: Decision) -> List[str]:
        """Handle noise gate enable/disable."""
        if not self.audio_capturer:
            raise RuntimeError("Audio capturer not available")
        
        params = decision.parameters
        enable = params.get("enable", True)
        threshold = params.get("threshold", 0.01)
        
        self.audio_capturer.config.enable_noise_gate = enable
        self.audio_capturer.config.noise_gate_threshold = threshold
        
        action = "enabled" if enable else "disabled"
        return [f"Noise gate {action} with threshold {threshold}"]
    
    # Model-related action handlers
    
    async def _handle_switch_model(self, decision: Decision) -> List[str]:
        """Handle model switching."""
        if not self.model_pool:
            raise RuntimeError("Model pool not available")
        
        params = decision.parameters
        target = params.get("target", "faster")
        reason = params.get("reason", "performance")
        
        # This would call model pool's switch_model method
        self.logger.info("Model switch requested", target=target, reason=reason)
        
        metrics_collector.increment_counter("model_switches")
        
        return [f"Model switched to {target} (reason: {reason})"]
    
    async def _handle_adjust_model_params(self, decision: Decision) -> List[str]:
        """Handle model parameter adjustment."""
        params = decision.parameters
        
        self.logger.info("Model parameter adjustment", params=params)
        
        return [f"Model parameters adjusted: {params}"]
    
    async def _handle_preload_model(self, decision: Decision) -> List[str]:
        """Handle model preloading."""
        if not self.model_pool:
            raise RuntimeError("Model pool not available")
        
        params = decision.parameters
        model_name = params.get("model_name", "fallback")
        
        self.logger.info("Model preload requested", model=model_name)
        
        return [f"Model {model_name} preloaded"]
    
    async def _handle_unload_model(self, decision: Decision) -> List[str]:
        """Handle model unloading."""
        if not self.model_pool:
            raise RuntimeError("Model pool not available")
        
        params = decision.parameters
        target = params.get("target", "unused")
        
        self.logger.info("Model unload requested", target=target)
        
        return [f"Unloaded {target} models"]
    
    # Performance-related action handlers
    
    async def _handle_adjust_chunk_size(self, decision: Decision) -> List[str]:
        """Handle chunk size adjustment."""
        params = decision.parameters
        action = params.get("action", "adjust")
        factor = params.get("factor", 1.0)
        
        if self.audio_capturer:
            current_size = self.audio_capturer.config.chunk_size
            
            if action == "increase":
                new_size = int(current_size * factor)
            elif action == "decrease":
                new_size = int(current_size * factor)
            else:
                new_size = params.get("size", current_size)
            
            # Apply limits
            min_size = 256
            max_size = 4096
            new_size = max(min_size, min(new_size, max_size))
            
            self.audio_capturer.config.chunk_size = new_size
            
            return [f"Chunk size adjusted from {current_size} to {new_size}"]
        
        return ["Chunk size adjustment (no audio capturer)"]
    
    async def _handle_throttle_processing(self, decision: Decision) -> List[str]:
        """Handle processing throttling."""
        params = decision.parameters
        factor = params.get("factor", 0.8)
        
        # This would adjust processing intervals
        if self.ooda_controller:
            current_intervals = self.ooda_controller.intervals
            new_intervals = {}
            
            for phase, interval in current_intervals.items():
                new_intervals[f"{phase.value}_ms"] = int(interval * 1000 / factor)
            
            self.ooda_controller.update_intervals(**new_intervals)
            
            return [f"Processing throttled by factor {factor}"]
        
        return ["Processing throttling (no OODA controller)"]
    
    async def _handle_increase_workers(self, decision: Decision) -> List[str]:
        """Handle worker increase."""
        params = decision.parameters
        count = params.get("count", 1)
        
        self.logger.info("Worker increase requested", count=count)
        
        return [f"Increased workers by {count} (not implemented)"]
    
    async def _handle_decrease_workers(self, decision: Decision) -> List[str]:
        """Handle worker decrease."""
        params = decision.parameters
        count = params.get("count", 1)
        
        self.logger.info("Worker decrease requested", count=count)
        
        return [f"Decreased workers by {count} (not implemented)"]
    
    # System-related action handlers
    
    async def _handle_restart_component(self, decision: Decision) -> List[str]:
        """Handle component restart."""
        params = decision.parameters
        component = params.get("component", "unknown")
        
        if component == "audio_capture" and self.audio_capturer:
            if self.audio_capturer.is_capturing:
                await self.audio_capturer.stop_capture()
                await asyncio.sleep(0.1)  # Brief pause
                await self.audio_capturer.start_capture()
                return ["Audio capture restarted"]
        
        self.logger.info("Component restart requested", component=component)
        
        return [f"Component {component} restart requested (not fully implemented)"]
    
    async def _handle_enable_fallback(self, decision: Decision) -> List[str]:
        """Handle fallback enable."""
        params = decision.parameters
        component = params.get("component", "unknown")
        reason = params.get("reason", "performance")
        
        self.logger.info("Fallback enabled", component=component, reason=reason)
        
        return [f"Fallback enabled for {component} (reason: {reason})"]
    
    async def _handle_adjust_intervals(self, decision: Decision) -> List[str]:
        """Handle interval adjustment."""
        params = decision.parameters
        
        if self.ooda_controller:
            self.ooda_controller.update_intervals(**params)
            return [f"OODA intervals adjusted: {params}"]
        
        return ["Interval adjustment (no OODA controller)"]
    
    async def _handle_clear_buffers(self, decision: Decision) -> List[str]:
        """Handle buffer clearing."""
        params = decision.parameters
        target = params.get("target", "all")
        
        cleared = []
        
        if target in ["all", "audio"] and self.audio_capturer:
            self.audio_capturer.audio_buffer.clear()
            cleared.append("audio buffer")
        
        return [f"Cleared {', '.join(cleared)}"] if cleared else ["No buffers cleared"]
    
    def get_adaptation_stats(self) -> Dict[str, Any]:
        """Get adaptation engine statistics.
        
        Returns:
            Dictionary of statistics
        """
        if not self._adaptation_history:
            return {"total_adaptations": 0}
        
        recent_adaptations = self._adaptation_history[-100:]  # Last 100
        
        success_count = sum(1 for a in recent_adaptations if a.success)
        total_time = sum(a.execution_time_ms for a in recent_adaptations)
        
        # Count by decision type
        type_counts = {}
        for adaptation in recent_adaptations:
            decision_type = adaptation.decision.type.value
            type_counts[decision_type] = type_counts.get(decision_type, 0) + 1
        
        count = len(recent_adaptations)
        
        return {
            "total_adaptations": len(self._adaptation_history),
            "recent_adaptations": count,
            "success_rate": success_count / count if count > 0 else 0,
            "avg_execution_time_ms": total_time / count if count > 0 else 0,
            "adaptation_types": type_counts,
            "registered_handlers": len(self._action_handlers)
        }