# 🎤 AI Narration - Real-Time Speech-to-Text System

A sophisticated real-time speech-to-text transcription system built with the **OODA (Observe-Orient-Decide-Act)** framework for adaptive behavior and optimal performance.

## 🌟 Features

- **🚀 Real-time transcription** with sub-200ms target latency
- **🤖 Multiple AI models** (Faster-Whisper, Distil-Whisper)
- **🔄 OODA adaptive framework** for dynamic optimization
- **🖥️ Cross-platform support** (Windows, Linux, macOS)
- **⚡ GPU acceleration** for faster processing
- **📊 Built-in monitoring** with Prometheus metrics
- **🎯 Local processing** - no cloud dependencies
- **🔧 Highly configurable** with JSON settings

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Audio Input   │───▶│  OODA Controller │───▶│ Speech Models   │
│   (Microphone)  │    │                  │    │ (Whisper/Distil)│
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Ring Buffer    │    │   Adaptation     │    │  Transcription  │
│  (Streaming)    │    │    Engine        │    │     Output      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- **Python 3.8+**
- **Conda** (recommended) or pip
- **Microphone** for audio input
- **GPU** (optional, for faster processing)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd AI_Narration
```

2. **Install dependencies**
```bash
# Using conda (recommended)
conda create -n ai_narration python=3.10
conda activate ai_narration
pip install -r requirements.txt

# Or using pip directly
pip install -r requirements.txt
```

3. **Verify installation**
```bash
python main.py --help
```

## 🎮 Usage

### Basic Usage

```bash
# Run with default settings (CPU mode)
python main.py

# Test mode (30 seconds)
python main.py --test-mode

# With custom log level
python main.py --log-level DEBUG

# Disable OODA loop (simpler mode)
python main.py --no-ooda
```

### GPU Mode (Faster Processing)

**Prerequisites for GPU:**
- NVIDIA GPU with CUDA support
- CUDA Toolkit installed
- PyTorch with CUDA support

```bash
# Check GPU availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"

# Enable GPU acceleration (auto-detects best GPU)
python main.py --gpu

# GPU with specific device
python main.py --gpu --gpu-device cuda:0

# GPU test mode (recommended first run)
python main.py --gpu --test-mode

# GPU with optimized config
python main.py --gpu --config config/settings_gpu.json
```

**GPU Performance Benefits:**
- **2-5x faster** transcription speed
- **Lower latency** (target: 100ms vs 200ms)
- **Better real-time performance**
- **Larger models** support (base, large)

### Advanced Options

```bash
# Custom configuration file
python main.py --config custom_config.json

# Specific audio device
python main.py --device "USB Microphone"

# Custom model
python main.py --model faster-whisper-large

# Statistics reporting interval
python main.py --stats-interval 60
```

## ⚙️ Configuration

### GPU Configuration

Edit `config/settings.json` for GPU settings:

```json
{
  "models": {
    "primary": {
      "name": "faster-whisper",
      "model_size": "base",
      "device": "cuda",
      "compute_type": "float16"
    },
    "fallback": {
      "name": "distil-whisper", 
      "model_size": "distil-small.en",
      "device": "cuda"
    }
  },
  "performance": {
    "target_latency_ms": 100,
    "max_latency_ms": 200
  }
}
```

### Audio Configuration

```json
{
  "audio": {
    "sample_rate": 16000,
    "channels": 1,
    "chunk_size": 1024,
    "buffer_duration_ms": 500
  },
  "audio_backends": {
    "windows": {
      "preferred": "sounddevice",
      "fallback": "pyaudio"
    }
  }
}
```

## 🧪 Testing

### Simple Test
```bash
python test_simple.py
```

### GPU Benchmark Test
```bash
# Compare CPU vs GPU performance
python test_gpu.py
```

### Full System Test
```bash
# CPU mode
python main.py --test-mode --log-level INFO

# GPU mode
python main.py --gpu --test-mode --log-level INFO
```

### Performance Validation
```bash
# Test with different models
python main.py --gpu --model faster-whisper-base --test-mode
python main.py --gpu --model distil-small.en --test-mode
```

## 📊 Monitoring

### Metrics Dashboard
- **Prometheus metrics**: http://localhost:8080/metrics
- **Real-time statistics** in console logs
- **Performance monitoring** with detailed timing

### Key Metrics
- **Audio latency**: Target <200ms
- **Processing time**: Per chunk timing
- **Buffer utilization**: Memory usage
- **Model performance**: Accuracy and speed

## 🔧 Troubleshooting

### Common Issues

**1. Audio device not found**
```bash
# List available devices
python -c "import sounddevice as sd; print(sd.query_devices())"
```

**2. GPU not detected**
```bash
# Check CUDA availability
python -c "import torch; print(torch.cuda.is_available())"

# Check GPU details
python -c "import torch; print([torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())])"

# Install CUDA-enabled PyTorch (if needed)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

**3. Model loading fails**
```bash
# Clear model cache
rm -rf ~/.cache/huggingface/
```

**4. Buffer overruns**
- Increase `buffer_duration_ms` in config
- Enable GPU mode for faster processing
- Reduce `chunk_size` for lower latency

### Performance Optimization

**For CPU mode:**
- Use `distil-small.en` model
- Set `compute_type: "int8"`
- Increase `chunk_overlap_ms`

**For GPU mode:**
- Use `faster-whisper-base` or larger
- Set `compute_type: "float16"`
- Reduce `target_latency_ms`

## 📁 Project Structure

```
AI_Narration/
├── main.py                 # Main application entry
├── test_simple.py          # Simple functionality test
├── requirements.txt        # Python dependencies
├── config/
│   └── settings.json      # Configuration file
├── src/
│   ├── audio/             # Audio capture & processing
│   ├── models/            # Speech recognition models
│   ├── ooda/              # OODA framework implementation
│   ├── streaming/         # Real-time streaming components
│   └── utils/             # Utilities & helpers
└── README.md              # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is open source. See LICENSE file for details.

## 🙏 Acknowledgments

- **OpenAI Whisper** for the base models
- **Hugging Face** for model hosting
- **OODA Loop** methodology for adaptive systems
