{"audio": {"sample_rate": 16000, "channels": 1, "chunk_size": 512, "buffer_duration_ms": 1000, "max_buffer_size_mb": 100, "silence_threshold": 0.01, "silence_duration_ms": 200}, "models": {"primary": {"name": "faster-whisper", "model_size": "base", "device": "cuda", "compute_type": "float16"}, "fallback": {"name": "distil-whisper", "model_size": "distil-small.en", "device": "cuda"}, "model_pool_size": 2, "warm_start": true}, "performance": {"target_latency_ms": 100, "max_latency_ms": 200, "transcription_timeout_ms": 3000, "chunk_overlap_ms": 25, "min_audio_length_ms": 50}, "ooda": {"observe_interval_ms": 5, "orient_interval_ms": 25, "decide_interval_ms": 50, "act_interval_ms": 5, "adaptation_threshold": 0.9, "performance_window_size": 50}, "system": {"log_level": "INFO", "enable_metrics": true, "metrics_port": 8080, "max_workers": 8, "graceful_shutdown_timeout": 5.0}, "audio_backends": {"windows": {"preferred": "sounddevice", "fallback": "pya<PERSON>o"}, "linux": {"preferred": "pulse", "fallback": "alsa"}, "darwin": {"preferred": "<PERSON><PERSON><PERSON>", "fallback": "portaudio"}}}