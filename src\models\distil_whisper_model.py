"""
Distil-Whisper model implementation for fast transcription.
"""

import asyncio
import gc
import time
from typing import Dict, Any, List, Optional
import numpy as np

from .base import BaseModel, ModelType, ModelState, TranscriptionResult, ModelLoadError, ModelTranscriptionError


class DistilWhisperModel(BaseModel):
    """Distil-Whisper model implementation for fast inference."""
    
    def __init__(self, 
                 model_size: str = "distil-small.en",
                 device: str = "auto",
                 torch_dtype: str = "float16"):
        """Initialize Distil-Whisper model.
        
        Args:
            model_size: Model size (distil-small.en, distil-medium.en, distil-large-v2)
            device: Device to run on ("cpu", "cuda", "auto")
            torch_dtype: PyTorch data type ("float16", "float32")
        """
        super().__init__(f"distil-whisper-{model_size}", ModelType.DISTIL_WHISPER)
        
        self.model_size = model_size
        self.device = device
        self.torch_dtype = torch_dtype
        
        # Pipeline components
        self._processor = None
        self._model = None
        self._pipeline = None
        
        # Model configuration
        self._supported_languages = ["en"]  # Distil-Whisper is English-only
        
        # Model size mapping
        self._model_id_map = {
            "distil-small.en": "distil-whisper/distil-small.en",
            "distil-medium.en": "distil-whisper/distil-medium.en", 
            "distil-large-v2": "distil-whisper/distil-large-v2",
            "distil-large-v3": "distil-whisper/distil-large-v3"
        }
        
    async def load(self, **kwargs) -> None:
        """Load the Distil-Whisper model."""
        if self.state == ModelState.LOADED:
            return
        
        if self.state == ModelState.LOADING:
            # Wait for loading to complete
            while self.state == ModelState.LOADING:
                await asyncio.sleep(0.1)
            return
        
        self.state = ModelState.LOADING
        load_start_time = time.time()
        
        try:
            self.logger.info(
                "Loading Distil-Whisper model",
                model_size=self.model_size,
                device=self.device,
                torch_dtype=self.torch_dtype
            )
            
            # Load model in thread to avoid blocking
            await asyncio.get_event_loop().run_in_executor(
                None, self._load_model_sync
            )
            
            self._load_time = time.time() - load_start_time
            self.state = ModelState.LOADED
            
            # Update memory usage
            self.metrics.memory_usage_mb = self.get_memory_usage_mb()
            
            self.logger.info(
                "Distil-Whisper model loaded",
                model_size=self.model_size,
                load_time_s=self._load_time,
                memory_mb=self.metrics.memory_usage_mb
            )
            
        except Exception as e:
            self.state = ModelState.ERROR
            self.logger.error("Failed to load Distil-Whisper model", error=str(e))
            raise ModelLoadError(f"Failed to load Distil-Whisper model: {e}")
    
    def _load_model_sync(self):
        """Load model synchronously (runs in thread)."""
        try:
            import torch
            from transformers import (
                AutoModelForSpeechSeq2Seq, 
                AutoProcessor,
                pipeline
            )
            
            # Get model ID
            model_id = self._model_id_map.get(self.model_size, self.model_size)
            
            # Determine device
            if self.device == "auto":
                device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                device = self.device
            
            # Determine torch dtype
            if self.torch_dtype == "float16":
                torch_dtype = torch.float16
            else:
                torch_dtype = torch.float32
            
            # Load model
            self._model = AutoModelForSpeechSeq2Seq.from_pretrained(
                model_id,
                torch_dtype=torch_dtype,
                low_cpu_mem_usage=True,
                use_safetensors=True
            )
            
            # Move to device
            self._model.to(device)
            
            # Load processor
            self._processor = AutoProcessor.from_pretrained(model_id)
            
            # Create pipeline
            self._pipeline = pipeline(
                "automatic-speech-recognition",
                model=self._model,
                tokenizer=self._processor.tokenizer,
                feature_extractor=self._processor.feature_extractor,
                max_new_tokens=128,
                chunk_length_s=30,
                batch_size=16,
                return_timestamps=True,
                torch_dtype=torch_dtype,
                device=device
            )
            
            self.device = device  # Update with actual device used
            
        except ImportError as e:
            raise ModelLoadError("transformers not installed. Install with: pip install transformers torch")
        except Exception as e:
            raise ModelLoadError(f"Failed to load Distil-Whisper model: {e}")
    
    async def unload(self) -> None:
        """Unload the model to free memory."""
        if self.state == ModelState.UNLOADED:
            return
        
        self.logger.info("Unloading Distil-Whisper model", model=self.model_name)
        
        try:
            # Delete components
            if self._pipeline is not None:
                del self._pipeline
                self._pipeline = None
            
            if self._model is not None:
                del self._model
                self._model = None
                
            if self._processor is not None:
                del self._processor
                self._processor = None
            
            # Force garbage collection
            gc.collect()
            
            # Clear CUDA cache if available
            try:
                import torch
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            
            self.state = ModelState.UNLOADED
            self.metrics.memory_usage_mb = 0.0
            
            self.logger.info("Distil-Whisper model unloaded")
            
        except Exception as e:
            self.logger.error("Error unloading model", error=str(e))
    
    async def transcribe(self, 
                        audio_data: np.ndarray, 
                        sample_rate: int = 16000,
                        language: Optional[str] = None,
                        return_timestamps: bool = False,
                        **kwargs) -> TranscriptionResult:
        """Transcribe audio using Distil-Whisper.
        
        Args:
            audio_data: Audio samples as numpy array
            sample_rate: Audio sample rate
            language: Language code (ignored - Distil-Whisper is English-only)
            return_timestamps: Whether to return word-level timestamps
            **kwargs: Additional parameters
            
        Returns:
            Transcription result
        """
        if not self.is_loaded():
            raise ModelTranscriptionError("Model is not loaded")
        
        try:
            # Ensure audio is float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Resample if needed (Distil-Whisper expects 16kHz)
            if sample_rate != 16000:
                audio_data = await self._resample_audio(audio_data, sample_rate, 16000)
            
            # Normalize audio
            if len(audio_data) > 0:
                max_val = np.max(np.abs(audio_data))
                if max_val > 0:
                    audio_data = audio_data / max_val
            
            # Prepare input
            inputs = {
                "raw": audio_data,
                "sampling_rate": 16000
            }
            
            # Run transcription in executor
            result = await asyncio.get_event_loop().run_in_executor(
                None, self._transcribe_sync, inputs, return_timestamps
            )
            
            return result
            
        except Exception as e:
            self.logger.error("Transcription error", error=str(e))
            raise ModelTranscriptionError(f"Transcription failed: {e}")
    
    def _transcribe_sync(self, inputs: Dict[str, Any], return_timestamps: bool) -> TranscriptionResult:
        """Synchronous transcription (runs in thread)."""
        try:
            # Generate transcription
            result = self._pipeline(
                inputs,
                return_timestamps=return_timestamps,
                generate_kwargs={"max_new_tokens": 256}
            )
            
            # Extract text
            text = result.get("text", "").strip()
            
            # Extract timestamps if available
            chunks = result.get("chunks", [])
            segments = []
            word_timestamps = []
            
            for chunk in chunks:
                segment = {
                    "start": chunk.get("timestamp", [0, 0])[0],
                    "end": chunk.get("timestamp", [0, 0])[1],
                    "text": chunk.get("text", "").strip()
                }
                segments.append(segment)
                
                # For word-level timestamps, we'd need additional processing
                # Distil-Whisper doesn't provide word-level timestamps by default
            
            # Estimate confidence (Distil-Whisper doesn't provide confidence scores)
            # Use a heuristic based on text length and model characteristics
            confidence = min(0.95, 0.7 + (len(text) / 1000) * 0.2) if text else 0.0
            
            return TranscriptionResult(
                text=text,
                confidence=confidence,
                processing_time_ms=0.0,  # Will be set by caller
                language="en",  # Distil-Whisper is English-only
                segments=segments,
                word_timestamps=word_timestamps
            )
            
        except Exception as e:
            raise ModelTranscriptionError(f"Distil-Whisper transcription failed: {e}")
    
    async def _resample_audio(self, audio_data: np.ndarray, 
                            from_rate: int, to_rate: int) -> np.ndarray:
        """Resample audio data.
        
        Args:
            audio_data: Input audio samples
            from_rate: Source sample rate
            to_rate: Target sample rate
            
        Returns:
            Resampled audio data
        """
        if from_rate == to_rate:
            return audio_data
        
        try:
            # Use librosa if available (preferred for audio processing)
            import librosa
            resampled = librosa.resample(
                audio_data, 
                orig_sr=from_rate, 
                target_sr=to_rate
            )
            return resampled.astype(np.float32)
            
        except ImportError:
            try:
                # Fallback to scipy
                from scipy.signal import resample
                new_length = int(len(audio_data) * to_rate / from_rate)
                resampled = resample(audio_data, new_length)
                return resampled.astype(np.float32)
                
            except ImportError:
                # Simple linear interpolation fallback
                ratio = to_rate / from_rate
                new_length = int(len(audio_data) * ratio)
                old_indices = np.linspace(0, len(audio_data) - 1, new_length)
                new_audio = np.interp(old_indices, np.arange(len(audio_data)), audio_data)
                return new_audio.astype(np.float32)
    
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        if not self.is_loaded():
            return 0.0
        
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert to MB
            
        except ImportError:
            # Fallback estimate based on model size
            size_estimates = {
                "distil-small.en": 166,
                "distil-medium.en": 394,
                "distil-large-v2": 756,
                "distil-large-v3": 756
            }
            return size_estimates.get(self.model_size, 200)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return self._supported_languages.copy()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get detailed model information.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_type": self.model_type.value,
            "model_size": self.model_size,
            "device": self.device,
            "torch_dtype": self.torch_dtype,
            "supported_languages": len(self._supported_languages),
            "supports_word_timestamps": False,  # Limited support
            "supports_language_detection": False,  # English-only
            "supports_translation": False,  # English-only
            "supports_vad": False,  # Not built-in
            "model_id": self._model_id_map.get(self.model_size, self.model_size),
            "optimized_for": "speed"
        }