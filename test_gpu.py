#!/usr/bin/env python3
"""
GPU performance test for the AI Narration system.
Tests GPU acceleration and compares performance with CPU mode.
"""

import asyncio
import time
import torch
import numpy as np
from src.models.distil_whisper_model import DistilWhisperModel
from src.models.faster_whisper_model import FasterWhisperModel
from src.audio.capture import AudioCapturer, AudioCapturerConfig

async def test_gpu_availability():
    """Test GPU availability and specs."""
    print("🔍 GPU Availability Check")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available - GPU acceleration disabled")
        return False
    
    device_count = torch.cuda.device_count()
    print(f"✅ CUDA available with {device_count} device(s)")
    
    for i in range(device_count):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / 1024**3
        print(f"   GPU {i}: {props.name} ({memory_gb:.1f}GB)")
    
    return True

async def benchmark_model(model_class, model_config, test_audio, device="cpu"):
    """Benchmark a model on CPU vs GPU."""
    print(f"\n🧪 Testing {model_class.__name__} on {device.upper()}")
    
    # Create model
    if model_class == FasterWhisperModel:
        model = model_class(
            model_size=model_config["model_size"],
            device=device,
            compute_type=model_config.get("compute_type", "default")
        )
    else:  # DistilWhisperModel
        model = model_class(
            model_size=model_config["model_size"],
            device=device,
            torch_dtype="float16" if device.startswith("cuda") else "float32"
        )
    
    try:
        # Load model and measure time
        load_start = time.time()
        await model.load()
        load_time = time.time() - load_start
        
        memory_mb = model.get_memory_usage_mb()
        print(f"   ✅ Loaded in {load_time:.1f}s, Memory: {memory_mb:.1f}MB")
        
        # Benchmark transcription
        transcription_times = []
        for i in range(3):  # 3 test runs
            start_time = time.time()
            result = await model.transcribe(test_audio, 16000)
            transcription_time = (time.time() - start_time) * 1000  # ms
            transcription_times.append(transcription_time)
            
            if i == 0:  # Show result from first run
                print(f"   📝 Sample result: '{result.text[:50]}...'")
        
        avg_time = np.mean(transcription_times)
        min_time = np.min(transcription_times)
        print(f"   ⚡ Transcription: {avg_time:.1f}ms avg, {min_time:.1f}ms best")
        
        await model.unload()
        return {
            "load_time": load_time,
            "memory_mb": memory_mb,
            "avg_transcription_ms": avg_time,
            "min_transcription_ms": min_time
        }
        
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        try:
            await model.unload()
        except:
            pass
        return None

async def create_test_audio():
    """Create test audio data."""
    print("🎵 Creating test audio (5 seconds of sine wave)")
    
    sample_rate = 16000
    duration = 5.0
    frequency = 440  # A4 note
    
    t = np.linspace(0, duration, int(sample_rate * duration))
    # Create a sine wave with some variation
    audio = 0.3 * np.sin(2 * np.pi * frequency * t) * np.exp(-t * 0.1)
    
    return audio.astype(np.float32)

async def main():
    """Main GPU test function."""
    print("🚀 AI Narration GPU Performance Test")
    print("=" * 60)
    
    # Check GPU availability
    gpu_available = await test_gpu_availability()
    
    # Create test audio
    test_audio = await create_test_audio()
    
    # Test configurations
    models_to_test = [
        {
            "class": DistilWhisperModel,
            "config": {
                "model_size": "distil-small.en",
                "compute_type": "default"
            }
        }
    ]
    
    # Add Faster-Whisper if we want to test it
    # models_to_test.append({
    #     "class": FasterWhisperModel,
    #     "config": {
    #         "model_size": "base",
    #         "compute_type": "default"
    #     }
    # })
    
    results = {}
    
    # Test each model on CPU and GPU
    for model_info in models_to_test:
        model_name = model_info["class"].__name__
        results[model_name] = {}
        
        # Test CPU
        cpu_result = await benchmark_model(
            model_info["class"], 
            model_info["config"], 
            test_audio, 
            "cpu"
        )
        results[model_name]["cpu"] = cpu_result
        
        # Test GPU if available
        if gpu_available:
            gpu_result = await benchmark_model(
                model_info["class"], 
                model_info["config"], 
                test_audio, 
                "cuda:0"
            )
            results[model_name]["gpu"] = gpu_result
    
    # Print comparison
    print("\n📊 Performance Comparison")
    print("=" * 60)
    
    for model_name, model_results in results.items():
        print(f"\n🤖 {model_name}")
        
        cpu_result = model_results.get("cpu")
        gpu_result = model_results.get("gpu")
        
        if cpu_result:
            print(f"   CPU: {cpu_result['avg_transcription_ms']:.1f}ms avg, {cpu_result['memory_mb']:.1f}MB")
        
        if gpu_result:
            print(f"   GPU: {gpu_result['avg_transcription_ms']:.1f}ms avg, {gpu_result['memory_mb']:.1f}MB")
            
            if cpu_result and gpu_result:
                speedup = cpu_result['avg_transcription_ms'] / gpu_result['avg_transcription_ms']
                print(f"   🚀 GPU Speedup: {speedup:.1f}x faster")
    
    print("\n✅ GPU test completed!")

if __name__ == "__main__":
    asyncio.run(main())
