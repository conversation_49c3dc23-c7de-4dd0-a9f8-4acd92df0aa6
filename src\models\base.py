"""
Base model interface and common functionality.
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional, List, Tuple
import numpy as np

from ..utils.logging import LoggerMixin


class ModelType(Enum):
    """Types of speech recognition models."""
    FASTER_WHISPER = "faster-whisper"
    DISTIL_WHISPER = "distil-whisper"
    OPENAI_WHISPER = "openai-whisper"


class ModelState(Enum):
    """Model loading states."""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"


@dataclass
class TranscriptionResult:
    """Result of a transcription operation."""
    text: str
    confidence: float
    processing_time_ms: float
    language: Optional[str] = None
    segments: Optional[List[Dict[str, Any]]] = None
    word_timestamps: Optional[List[Dict[str, Any]]] = None
    
    def __post_init__(self):
        if self.segments is None:
            self.segments = []
        if self.word_timestamps is None:
            self.word_timestamps = []


@dataclass
class ModelMetrics:
    """Performance metrics for a model."""
    total_transcriptions: int = 0
    total_processing_time_ms: float = 0.0
    avg_processing_time_ms: float = 0.0
    min_processing_time_ms: float = float('inf')
    max_processing_time_ms: float = 0.0
    success_count: int = 0
    error_count: int = 0
    avg_confidence: float = 0.0
    memory_usage_mb: float = 0.0
    
    def update(self, processing_time_ms: float, confidence: float, success: bool = True):
        """Update metrics with new transcription results.
        
        Args:
            processing_time_ms: Processing time in milliseconds
            confidence: Confidence score (0.0-1.0)
            success: Whether transcription was successful
        """
        self.total_transcriptions += 1
        
        if success:
            self.success_count += 1
            self.total_processing_time_ms += processing_time_ms
            self.avg_processing_time_ms = self.total_processing_time_ms / self.success_count
            self.min_processing_time_ms = min(self.min_processing_time_ms, processing_time_ms)
            self.max_processing_time_ms = max(self.max_processing_time_ms, processing_time_ms)
            
            # Update average confidence (simple moving average)
            if self.success_count == 1:
                self.avg_confidence = confidence
            else:
                self.avg_confidence = (self.avg_confidence * (self.success_count - 1) + confidence) / self.success_count
        else:
            self.error_count += 1
    
    @property
    def success_rate(self) -> float:
        """Get success rate as a percentage."""
        return (self.success_count / self.total_transcriptions) * 100 if self.total_transcriptions > 0 else 0.0
    
    @property
    def error_rate(self) -> float:
        """Get error rate as a percentage."""
        return (self.error_count / self.total_transcriptions) * 100 if self.total_transcriptions > 0 else 0.0


class BaseModel(ABC, LoggerMixin):
    """Base class for speech recognition models."""
    
    def __init__(self, model_name: str, model_type: ModelType):
        """Initialize base model.
        
        Args:
            model_name: Name/identifier of the model
            model_type: Type of the model
        """
        super().__init__()
        self.model_name = model_name
        self.model_type = model_type
        self.state = ModelState.UNLOADED
        self._model = None
        self._load_time = 0.0
        self._last_used_time = 0.0
        self.metrics = ModelMetrics()
        
    @abstractmethod
    async def load(self, **kwargs) -> None:
        """Load the model.
        
        Args:
            **kwargs: Model-specific loading parameters
            
        Raises:
            RuntimeError: If model loading fails
        """
        pass
    
    @abstractmethod
    async def unload(self) -> None:
        """Unload the model to free memory."""
        pass
    
    @abstractmethod
    async def transcribe(self, 
                        audio_data: np.ndarray, 
                        sample_rate: int = 16000,
                        **kwargs) -> TranscriptionResult:
        """Transcribe audio data.
        
        Args:
            audio_data: Audio samples as numpy array
            sample_rate: Audio sample rate
            **kwargs: Model-specific transcription parameters
            
        Returns:
            Transcription result
            
        Raises:
            RuntimeError: If transcription fails
        """
        pass
    
    @abstractmethod
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB.
        
        Returns:
            Memory usage in megabytes
        """
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages.
        
        Returns:
            List of language codes
        """
        pass
    
    def is_loaded(self) -> bool:
        """Check if model is loaded.
        
        Returns:
            True if model is loaded and ready
        """
        return self.state == ModelState.LOADED
    
    def is_loading(self) -> bool:
        """Check if model is currently loading.
        
        Returns:
            True if model is loading
        """
        return self.state == ModelState.LOADING
    
    def get_load_time(self) -> float:
        """Get model load time in seconds.
        
        Returns:
            Load time in seconds
        """
        return self._load_time
    
    def get_idle_time(self) -> float:
        """Get time since last use in seconds.
        
        Returns:
            Idle time in seconds
        """
        if self._last_used_time == 0:
            return 0.0
        return time.time() - self._last_used_time
    
    def mark_used(self):
        """Mark model as recently used."""
        self._last_used_time = time.time()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get model statistics.
        
        Returns:
            Dictionary of model statistics
        """
        return {
            "model_name": self.model_name,
            "model_type": self.model_type.value,
            "state": self.state.value,
            "is_loaded": self.is_loaded(),
            "load_time_s": self._load_time,
            "idle_time_s": self.get_idle_time(),
            "memory_usage_mb": self.get_memory_usage_mb() if self.is_loaded() else 0.0,
            "metrics": {
                "total_transcriptions": self.metrics.total_transcriptions,
                "success_rate": self.metrics.success_rate,
                "error_rate": self.metrics.error_rate,
                "avg_processing_time_ms": self.metrics.avg_processing_time_ms,
                "min_processing_time_ms": self.metrics.min_processing_time_ms if self.metrics.min_processing_time_ms != float('inf') else 0,
                "max_processing_time_ms": self.metrics.max_processing_time_ms,
                "avg_confidence": self.metrics.avg_confidence
            }
        }
    
    async def _safe_transcribe(self, 
                             audio_data: np.ndarray, 
                             sample_rate: int = 16000,
                             **kwargs) -> TranscriptionResult:
        """Safely transcribe with error handling and metrics.
        
        Args:
            audio_data: Audio samples
            sample_rate: Sample rate
            **kwargs: Additional parameters
            
        Returns:
            Transcription result
        """
        if not self.is_loaded():
            raise RuntimeError(f"Model {self.model_name} is not loaded")
        
        start_time = time.time()
        self.mark_used()
        
        try:
            result = await self.transcribe(audio_data, sample_rate, **kwargs)
            
            processing_time_ms = (time.time() - start_time) * 1000.0
            result.processing_time_ms = processing_time_ms
            
            # Update metrics
            self.metrics.update(processing_time_ms, result.confidence, success=True)
            
            self.logger.debug(
                "Transcription completed",
                model=self.model_name,
                processing_time_ms=processing_time_ms,
                confidence=result.confidence,
                text_length=len(result.text)
            )
            
            return result
            
        except Exception as e:
            processing_time_ms = (time.time() - start_time) * 1000.0
            self.metrics.update(processing_time_ms, 0.0, success=False)
            
            self.logger.error(
                "Transcription failed",
                model=self.model_name,
                error=str(e),
                processing_time_ms=processing_time_ms
            )
            
            raise
    
    def __str__(self) -> str:
        """String representation of the model."""
        return f"{self.model_type.value}:{self.model_name}({self.state.value})"
    
    def __repr__(self) -> str:
        """Detailed representation of the model."""
        return (f"<{self.__class__.__name__}("
                f"name='{self.model_name}', "
                f"type={self.model_type.value}, "
                f"state={self.state.value}, "
                f"memory={self.get_memory_usage_mb():.1f}MB)>")


class ModelError(Exception):
    """Base exception for model-related errors."""
    pass


class ModelLoadError(ModelError):
    """Exception raised when model loading fails."""
    pass


class ModelTranscriptionError(ModelError):
    """Exception raised when transcription fails."""
    pass