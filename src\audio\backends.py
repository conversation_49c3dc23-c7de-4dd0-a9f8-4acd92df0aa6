"""
Cross-platform audio backend implementations.
"""

import asyncio
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, Callable, Any, Dict
import numpy as np

from ..utils.logging import LoggerMixin
from ..utils.platform import platform_detector, AudioBackendType
from ..utils.metrics import metrics_collector
from .device import AudioDevice


@dataclass
class AudioConfig:
    """Audio configuration for backends."""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    dtype: np.dtype = np.float32
    device_id: Optional[str] = None


class AudioBackend(ABC, LoggerMixin):
    """Abstract base class for audio backends."""
    
    def __init__(self, config: AudioConfig):
        """Initialize audio backend.
        
        Args:
            config: Audio configuration
        """
        super().__init__()
        self.config = config
        self.is_recording = False
        self._callback: Optional[Callable[[np.ndarray, float], None]] = None
        
    @abstractmethod
    async def start_recording(self, callback: Callable[[np.ndarray, float], None]) -> None:
        """Start audio recording.
        
        Args:
            callback: Function to call with audio data and timestamp
        """
        pass
    
    @abstractmethod
    async def stop_recording(self) -> None:
        """Stop audio recording."""
        pass
    
    @abstractmethod
    def get_latency_ms(self) -> float:
        """Get current audio latency in milliseconds."""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if backend is available on current system."""
        pass


class SoundDeviceBackend(AudioBackend):
    """SoundDevice backend for cross-platform audio."""
    
    def __init__(self, config: AudioConfig):
        """Initialize SoundDevice backend."""
        super().__init__(config)
        self._stream = None
        self._loop = None
        self._last_callback_time = 0
        
    def is_available(self) -> bool:
        """Check if SoundDevice is available."""
        try:
            import sounddevice as sd
            return True
        except ImportError:
            return False
    
    async def start_recording(self, callback: Callable[[np.ndarray, float], None]) -> None:
        """Start recording with SoundDevice."""
        if not self.is_available():
            raise RuntimeError("SoundDevice not available")
            
        import sounddevice as sd
        
        self._callback = callback
        self._loop = asyncio.get_event_loop()
        
        def audio_callback(indata, frames, time_info, status):
            """Audio callback for SoundDevice."""
            if status:
                self.logger.warning("SoundDevice status", status=status)
                
            # Convert to numpy array
            audio_data = indata.copy()
            if self.config.channels == 1:
                audio_data = audio_data.flatten()
                
            # Get timestamp
            timestamp = time.time()
            self._last_callback_time = timestamp
            
            # Call user callback in async loop
            if self._callback:
                self._loop.call_soon_threadsafe(
                    lambda: asyncio.create_task(
                        self._async_callback_wrapper(audio_data, timestamp)
                    )
                )
        
        try:
            self._stream = sd.InputStream(
                samplerate=self.config.sample_rate,
                channels=self.config.channels,
                dtype=self.config.dtype,
                blocksize=self.config.chunk_size,
                device=self.config.device_id,
                callback=audio_callback,
                latency='low'
            )
            
            self._stream.start()
            self.is_recording = True
            
            self.logger.info(
                "Started SoundDevice recording",
                sample_rate=self.config.sample_rate,
                channels=self.config.channels,
                chunk_size=self.config.chunk_size
            )
            
        except Exception as e:
            self.logger.error("Failed to start SoundDevice recording", error=str(e))
            raise
    
    async def _async_callback_wrapper(self, audio_data: np.ndarray, timestamp: float):
        """Wrapper to call user callback asynchronously."""
        try:
            await self._callback(audio_data, timestamp)
        except Exception as e:
            self.logger.error("Error in audio callback", error=str(e))
    
    async def stop_recording(self) -> None:
        """Stop SoundDevice recording."""
        if self._stream:
            try:
                self._stream.stop()
                self._stream.close()
            except Exception as e:
                self.logger.error("Error stopping SoundDevice", error=str(e))
            finally:
                self._stream = None
                self.is_recording = False
                self.logger.info("Stopped SoundDevice recording")
    
    def get_latency_ms(self) -> float:
        """Get SoundDevice latency."""
        if self._stream:
            try:
                return self._stream.latency * 1000.0
            except:
                pass
        return self.config.chunk_size / self.config.sample_rate * 1000.0


class PyAudioBackend(AudioBackend):
    """PyAudio backend for audio capture."""
    
    def __init__(self, config: AudioConfig):
        """Initialize PyAudio backend."""
        super().__init__(config)
        self._pa = None
        self._stream = None
        self._recording_thread = None
        self._stop_event = threading.Event()
        
    def is_available(self) -> bool:
        """Check if PyAudio is available."""
        try:
            import pyaudio
            return True
        except ImportError:
            return False
    
    async def start_recording(self, callback: Callable[[np.ndarray, float], None]) -> None:
        """Start recording with PyAudio."""
        if not self.is_available():
            raise RuntimeError("PyAudio not available")
            
        import pyaudio
        
        self._callback = callback
        self._pa = pyaudio.PyAudio()
        
        try:
            self._stream = self._pa.open(
                format=pyaudio.paFloat32,
                channels=self.config.channels,
                rate=self.config.sample_rate,
                input=True,
                frames_per_buffer=self.config.chunk_size,
                input_device_index=int(self.config.device_id) if self.config.device_id else None
            )
            
            self._stop_event.clear()
            self._recording_thread = threading.Thread(target=self._recording_loop)
            self._recording_thread.start()
            
            self.is_recording = True
            
            self.logger.info(
                "Started PyAudio recording",
                sample_rate=self.config.sample_rate,
                channels=self.config.channels,
                chunk_size=self.config.chunk_size
            )
            
        except Exception as e:
            self.logger.error("Failed to start PyAudio recording", error=str(e))
            if self._pa:
                self._pa.terminate()
                self._pa = None
            raise
    
    def _recording_loop(self):
        """Recording loop for PyAudio."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            while not self._stop_event.is_set():
                try:
                    # Read audio data
                    audio_bytes = self._stream.read(
                        self.config.chunk_size,
                        exception_on_overflow=False
                    )
                    
                    # Convert to numpy array
                    audio_data = np.frombuffer(audio_bytes, dtype=np.float32)
                    if self.config.channels == 1:
                        audio_data = audio_data.flatten()
                    else:
                        audio_data = audio_data.reshape(-1, self.config.channels)
                    
                    timestamp = time.time()
                    
                    # Call user callback
                    if self._callback:
                        loop.run_until_complete(self._callback(audio_data, timestamp))
                        
                except Exception as e:
                    if not self._stop_event.is_set():
                        self.logger.error("Error in PyAudio recording loop", error=str(e))
                        
        finally:
            loop.close()
    
    async def stop_recording(self) -> None:
        """Stop PyAudio recording."""
        self._stop_event.set()
        
        if self._recording_thread:
            self._recording_thread.join(timeout=1.0)
            
        if self._stream:
            try:
                self._stream.stop_stream()
                self._stream.close()
            except Exception as e:
                self.logger.error("Error stopping PyAudio stream", error=str(e))
            finally:
                self._stream = None
                
        if self._pa:
            try:
                self._pa.terminate()
            except Exception as e:
                self.logger.error("Error terminating PyAudio", error=str(e))
            finally:
                self._pa = None
                
        self.is_recording = False
        self.logger.info("Stopped PyAudio recording")
    
    def get_latency_ms(self) -> float:
        """Get PyAudio latency estimate."""
        if self._stream:
            try:
                latency = self._stream.get_input_latency()
                return latency * 1000.0
            except:
                pass
        return self.config.chunk_size / self.config.sample_rate * 1000.0


class PulseAudioBackend(AudioBackend):
    """PulseAudio backend for Linux."""
    
    def __init__(self, config: AudioConfig):
        """Initialize PulseAudio backend."""
        super().__init__(config)
        self._pulse = None
        self._recording_task = None
        
    def is_available(self) -> bool:
        """Check if PulseAudio is available."""
        try:
            import pulsectl
            return True
        except ImportError:
            return False
    
    async def start_recording(self, callback: Callable[[np.ndarray, float], None]) -> None:
        """Start recording with PulseAudio."""
        if not self.is_available():
            raise RuntimeError("PulseAudio not available")
            
        # This is a simplified implementation
        # Full implementation would use pulsectl to create recording streams
        self.logger.warning("PulseAudio backend not fully implemented, falling back")
        raise NotImplementedError("PulseAudio backend not fully implemented")
    
    async def stop_recording(self) -> None:
        """Stop PulseAudio recording."""
        pass
    
    def get_latency_ms(self) -> float:
        """Get PulseAudio latency."""
        return 50.0  # Typical PulseAudio latency


class WASAPIBackend(AudioBackend):
    """WASAPI backend for Windows."""
    
    def __init__(self, config: AudioConfig):
        """Initialize WASAPI backend."""
        super().__init__(config)
        
    def is_available(self) -> bool:
        """Check if WASAPI is available."""
        try:
            import ctypes
            import platform
            return platform.system() == "Windows"
        except ImportError:
            return False
    
    async def start_recording(self, callback: Callable[[np.ndarray, float], None]) -> None:
        """Start recording with WASAPI."""
        if not self.is_available():
            raise RuntimeError("WASAPI not available")
            
        # This is a simplified implementation
        # Full implementation would use Windows Core Audio APIs
        self.logger.warning("WASAPI backend not fully implemented, falling back")
        raise NotImplementedError("WASAPI backend not fully implemented")
    
    async def stop_recording(self) -> None:
        """Stop WASAPI recording."""
        pass
    
    def get_latency_ms(self) -> float:
        """Get WASAPI latency."""
        return 10.0  # WASAPI can achieve very low latency


def get_audio_backend(config: AudioConfig, preferred_backend: Optional[str] = None) -> AudioBackend:
    """Get the best available audio backend for the current platform.
    
    Args:
        config: Audio configuration
        preferred_backend: Preferred backend name
        
    Returns:
        Audio backend instance
        
    Raises:
        RuntimeError: If no suitable backend is available
    """
    platform_info = platform_detector.detect_platform()
    
    # Determine backend preference order
    if preferred_backend:
        backends_to_try = [preferred_backend]
        # Add fallbacks
        if preferred_backend != "sounddevice":
            backends_to_try.append("sounddevice")
        if preferred_backend != "pyaudio":
            backends_to_try.append("pyaudio")
    else:
        # Platform-specific preference order
        if platform_info.platform_type.value == "windows":
            backends_to_try = ["wasapi", "sounddevice", "pyaudio"]
        elif platform_info.platform_type.value == "linux":
            backends_to_try = ["pulse", "sounddevice", "pyaudio"]
        else:
            backends_to_try = ["sounddevice", "pyaudio"]
    
    # Try backends in order
    for backend_name in backends_to_try:
        try:
            if backend_name == "sounddevice":
                backend = SoundDeviceBackend(config)
            elif backend_name == "pyaudio":
                backend = PyAudioBackend(config)
            elif backend_name == "pulse":
                backend = PulseAudioBackend(config)
            elif backend_name == "wasapi":
                backend = WASAPIBackend(config)
            else:
                continue
                
            if backend.is_available():
                return backend
                
        except Exception as e:
            # Log but continue to next backend
            pass
    
    # If no backend works, raise error
    raise RuntimeError(
        f"No suitable audio backend available. Tried: {backends_to_try}. "
        "Please install sounddevice (pip install sounddevice) or pyaudio."
    )