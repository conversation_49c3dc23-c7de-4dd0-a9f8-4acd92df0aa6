"""
Real-time audio stream processing with sub-300ms latency target.
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Optional, Callable, Dict, Any, List
import numpy as np

from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector
from .ring_buffer import AudioRingBuffer


@dataclass
class ProcessingChunk:
    """Audio chunk for processing."""
    audio_data: np.ndarray
    timestamp: float
    chunk_id: int
    sample_rate: int
    confidence: float = 0.0
    processing_start_time: float = 0.0
    
    def __post_init__(self):
        if self.processing_start_time == 0:
            self.processing_start_time = time.time()


@dataclass
class ProcessingResult:
    """Result of audio processing."""
    chunk_id: int
    text: str
    confidence: float
    processing_time_ms: float
    timestamp: float
    is_final: bool = True
    segments: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.segments is None:
            self.segments = []


class StreamProcessor(LoggerMixin):
    """Real-time audio stream processor with latency optimization."""
    
    def __init__(self,
                 target_latency_ms: int = 200,
                 max_latency_ms: int = 300,
                 chunk_duration_ms: int = 100,
                 overlap_ms: int = 50,
                 min_audio_length_ms: int = 500):
        """Initialize stream processor.
        
        Args:
            target_latency_ms: Target processing latency
            max_latency_ms: Maximum acceptable latency
            chunk_duration_ms: Audio chunk duration for processing
            overlap_ms: Overlap between consecutive chunks
            min_audio_length_ms: Minimum audio length before processing
        """
        super().__init__()
        
        self.target_latency_ms = target_latency_ms
        self.max_latency_ms = max_latency_ms
        self.chunk_duration_ms = chunk_duration_ms
        self.overlap_ms = overlap_ms
        self.min_audio_length_ms = min_audio_length_ms
        
        # Processing state
        self._is_processing = False
        self._processing_task: Optional[asyncio.Task] = None
        self._chunk_counter = 0
        
        # Buffers and queues
        self._input_buffer: Optional[AudioRingBuffer] = None
        self._processing_queue: asyncio.Queue = asyncio.Queue(maxsize=50)  # Increased from 10 to 50
        self._result_callbacks: List[Callable[[ProcessingResult], None]] = []
        
        # Performance tracking
        self._processing_times: List[float] = []
        self._max_performance_history = 100
        self._last_chunk_time = 0
        
        # Adaptive parameters
        self._adaptive_chunk_size = chunk_duration_ms
        self._adaptive_enabled = True
        
    def set_input_buffer(self, buffer: AudioRingBuffer):
        """Set the input audio buffer.
        
        Args:
            buffer: Audio ring buffer to read from
        """
        self._input_buffer = buffer
        self.logger.info("Input buffer set", 
                        capacity_samples=buffer.capacity,
                        sample_rate=buffer.sample_rate)
    
    def add_result_callback(self, callback: Callable[[ProcessingResult], None]):
        """Add a callback for processing results.
        
        Args:
            callback: Function to call with processing results
        """
        self._result_callbacks.append(callback)
        self.logger.debug("Added result callback", total_callbacks=len(self._result_callbacks))
    
    def remove_result_callback(self, callback: Callable[[ProcessingResult], None]):
        """Remove a result callback.
        
        Args:
            callback: Callback function to remove
        """
        if callback in self._result_callbacks:
            self._result_callbacks.remove(callback)
            self.logger.debug("Removed result callback", total_callbacks=len(self._result_callbacks))
    
    async def start_processing(self, processor_func: Callable[[ProcessingChunk], ProcessingResult]):
        """Start real-time processing.
        
        Args:
            processor_func: Async function to process audio chunks
        """
        if self._is_processing:
            raise RuntimeError("Stream processor is already running")
        
        if not self._input_buffer:
            raise RuntimeError("Input buffer not set")
        
        self.logger.info("Starting stream processor",
                        target_latency_ms=self.target_latency_ms,
                        chunk_duration_ms=self.chunk_duration_ms)
        
        self._is_processing = True
        self._chunk_counter = 0
        
        # Start processing task
        self._processing_task = asyncio.create_task(
            self._processing_loop(processor_func)
        )
        
        # Start chunk extraction task
        extraction_task = asyncio.create_task(self._extraction_loop())
        
        try:
            await asyncio.gather(self._processing_task, extraction_task)
        except asyncio.CancelledError:
            self.logger.info("Stream processor cancelled")
        finally:
            self._is_processing = False
    
    async def stop_processing(self):
        """Stop real-time processing."""
        if not self._is_processing:
            return
        
        self.logger.info("Stopping stream processor")
        self._is_processing = False
        
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        
        # Clear processing queue
        while not self._processing_queue.empty():
            try:
                self._processing_queue.get_nowait()
            except asyncio.QueueEmpty:
                break
        
        self.logger.info("Stream processor stopped")
    
    async def _extraction_loop(self):
        """Extract audio chunks from buffer for processing."""
        last_read_time = 0
        
        while self._is_processing:
            try:
                current_time = time.time()
                
                # Check if enough time has passed for next chunk
                time_since_last = (current_time - last_read_time) * 1000
                if time_since_last < self._adaptive_chunk_size:
                    await asyncio.sleep(0.001)  # 1ms sleep
                    continue
                
                # Check if enough audio is available
                if not self._input_buffer.has_minimum_audio(self.min_audio_length_ms):
                    await asyncio.sleep(0.005)  # 5ms sleep
                    continue
                
                # Extract audio chunk
                chunk_duration_ms = self._adaptive_chunk_size
                audio_data = self._input_buffer.read_duration(chunk_duration_ms, timeout=0.1)
                
                if len(audio_data) == 0:
                    continue
                
                # Create processing chunk
                chunk = ProcessingChunk(
                    audio_data=audio_data,
                    timestamp=current_time,
                    chunk_id=self._chunk_counter,
                    sample_rate=self._input_buffer.sample_rate
                )
                
                self._chunk_counter += 1
                last_read_time = current_time
                
                # Add to processing queue (non-blocking)
                try:
                    self._processing_queue.put_nowait(chunk)
                    self.logger.debug("Added chunk to processing queue",
                                    chunk_id=chunk.chunk_id,
                                    audio_length_ms=len(audio_data) / self._input_buffer.sample_rate * 1000)
                except asyncio.QueueFull:
                    # Drop oldest chunk if queue is full
                    try:
                        dropped_chunk = self._processing_queue.get_nowait()
                        self.logger.warning("Dropped chunk due to full queue",
                                          dropped_chunk_id=dropped_chunk.chunk_id)
                        metrics_collector.increment_counter("dropped_chunks")
                    except asyncio.QueueEmpty:
                        pass
                    
                    try:
                        self._processing_queue.put_nowait(chunk)
                    except asyncio.QueueFull:
                        self.logger.error("Failed to add chunk even after dropping")
                
            except Exception as e:
                self.logger.error("Error in extraction loop", error=str(e))
                await asyncio.sleep(0.01)
    
    async def _processing_loop(self, processor_func: Callable[[ProcessingChunk], ProcessingResult]):
        """Main processing loop."""
        while self._is_processing:
            try:
                # Get next chunk to process
                try:
                    chunk = await asyncio.wait_for(
                        self._processing_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Process chunk with latency tracking
                processing_start = time.time()
                
                try:
                    with metrics_collector.measure_latency("stream_processing"):
                        result = await processor_func(chunk)
                    
                    processing_time_ms = (time.time() - processing_start) * 1000.0
                    result.processing_time_ms = processing_time_ms
                    
                    # Track performance
                    self._processing_times.append(processing_time_ms)
                    if len(self._processing_times) > self._max_performance_history:
                        self._processing_times = self._processing_times[-self._max_performance_history:]
                    
                    # Check latency targets
                    total_latency_ms = (time.time() - chunk.timestamp) * 1000.0
                    
                    if total_latency_ms > self.max_latency_ms:
                        self.logger.warning("Latency exceeded maximum",
                                          total_latency_ms=total_latency_ms,
                                          processing_time_ms=processing_time_ms,
                                          chunk_id=chunk.chunk_id)
                        metrics_collector.increment_counter("latency_violations")
                        
                        # Trigger adaptive adjustments
                        if self._adaptive_enabled:
                            await self._adapt_to_latency(total_latency_ms)
                    
                    # Call result callbacks
                    for callback in self._result_callbacks:
                        try:
                            await callback(result)
                        except Exception as e:
                            self.logger.error("Error in result callback", error=str(e))
                    
                    self.logger.debug("Processed chunk",
                                    chunk_id=chunk.chunk_id,
                                    processing_time_ms=processing_time_ms,
                                    total_latency_ms=total_latency_ms,
                                    text_length=len(result.text))
                    
                except Exception as e:
                    processing_time_ms = (time.time() - processing_start) * 1000.0
                    self.logger.error("Chunk processing failed",
                                    chunk_id=chunk.chunk_id,
                                    processing_time_ms=processing_time_ms,
                                    error=str(e))
                    
                    metrics_collector.increment_counter(
                        "errors",
                        labels={"error_type": "processing", "component": "stream_processor"}
                    )
                
            except Exception as e:
                self.logger.error("Error in processing loop", error=str(e))
                await asyncio.sleep(0.01)
    
    async def _adapt_to_latency(self, current_latency_ms: float):
        """Adapt processing parameters based on current latency.
        
        Args:
            current_latency_ms: Current total latency
        """
        if current_latency_ms > self.max_latency_ms * 1.5:
            # Severe latency - reduce chunk size significantly
            new_chunk_size = max(50, self._adaptive_chunk_size * 0.7)
            self.logger.info("Severe latency detected, reducing chunk size",
                           old_size=self._adaptive_chunk_size,
                           new_size=new_chunk_size)
            self._adaptive_chunk_size = new_chunk_size
            
        elif current_latency_ms > self.max_latency_ms:
            # Moderate latency - small reduction
            new_chunk_size = max(50, self._adaptive_chunk_size * 0.9)
            self.logger.info("High latency detected, adjusting chunk size",
                           old_size=self._adaptive_chunk_size,
                           new_size=new_chunk_size)
            self._adaptive_chunk_size = new_chunk_size
    
    def _adapt_to_performance(self):
        """Adapt parameters based on recent performance."""
        if len(self._processing_times) < 10:
            return
        
        recent_times = self._processing_times[-20:]  # Last 20 measurements
        avg_processing_time = sum(recent_times) / len(recent_times)
        
        # If consistently fast, we can increase chunk size for better efficiency
        if avg_processing_time < self.target_latency_ms * 0.5:
            new_chunk_size = min(200, self._adaptive_chunk_size * 1.1)
            if new_chunk_size != self._adaptive_chunk_size:
                self.logger.debug("Fast processing detected, increasing chunk size",
                                old_size=self._adaptive_chunk_size,
                                new_size=new_chunk_size)
                self._adaptive_chunk_size = new_chunk_size
        
        # If consistently slow, reduce chunk size
        elif avg_processing_time > self.target_latency_ms:
            new_chunk_size = max(50, self._adaptive_chunk_size * 0.95)
            if new_chunk_size != self._adaptive_chunk_size:
                self.logger.debug("Slow processing detected, reducing chunk size",
                                old_size=self._adaptive_chunk_size,
                                new_size=new_chunk_size)
                self._adaptive_chunk_size = new_chunk_size
    
    def get_stats(self) -> Dict[str, Any]:
        """Get stream processor statistics.
        
        Returns:
            Dictionary of statistics
        """
        avg_processing_time = 0.0
        min_processing_time = 0.0
        max_processing_time = 0.0
        
        if self._processing_times:
            avg_processing_time = sum(self._processing_times) / len(self._processing_times)
            min_processing_time = min(self._processing_times)
            max_processing_time = max(self._processing_times)
        
        queue_size = self._processing_queue.qsize() if self._processing_queue else 0
        
        return {
            "is_processing": self._is_processing,
            "target_latency_ms": self.target_latency_ms,
            "max_latency_ms": self.max_latency_ms,
            "chunk_duration_ms": self.chunk_duration_ms,
            "adaptive_chunk_size_ms": self._adaptive_chunk_size,
            "overlap_ms": self.overlap_ms,
            "min_audio_length_ms": self.min_audio_length_ms,
            "total_chunks_processed": self._chunk_counter,
            "queue_size": queue_size,
            "queue_max_size": self._processing_queue.maxsize if self._processing_queue else 0,
            "result_callbacks": len(self._result_callbacks),
            "performance": {
                "avg_processing_time_ms": avg_processing_time,
                "min_processing_time_ms": min_processing_time,
                "max_processing_time_ms": max_processing_time,
                "samples_count": len(self._processing_times)
            },
            "adaptive_enabled": self._adaptive_enabled
        }
    
    def set_adaptive_enabled(self, enabled: bool):
        """Enable or disable adaptive processing.
        
        Args:
            enabled: Whether to enable adaptive processing
        """
        self._adaptive_enabled = enabled
        self.logger.info("Adaptive processing", enabled=enabled)
    
    def reset_adaptive_parameters(self):
        """Reset adaptive parameters to defaults."""
        self._adaptive_chunk_size = self.chunk_duration_ms
        self._processing_times.clear()
        self.logger.info("Reset adaptive parameters")
    
    @property
    def is_processing(self) -> bool:
        """Check if processor is currently processing."""
        return self._is_processing