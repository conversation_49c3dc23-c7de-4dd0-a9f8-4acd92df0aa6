{"audio": {"sample_rate": 16000, "channels": 1, "chunk_size": 512, "buffer_duration_ms": 1000, "max_buffer_size_mb": 50, "silence_threshold": 0.01, "silence_duration_ms": 300}, "models": {"primary": {"name": "faster-whisper", "model_size": "base", "device": "cpu", "compute_type": "default"}, "fallback": {"name": "distil-whisper", "model_size": "distil-small.en", "device": "cpu"}, "model_pool_size": 2, "warm_start": true}, "performance": {"target_latency_ms": 500, "max_latency_ms": 1000, "transcription_timeout_ms": 5000, "chunk_overlap_ms": 50, "min_audio_length_ms": 100}, "ooda": {"observe_interval_ms": 50, "orient_interval_ms": 200, "decide_interval_ms": 500, "act_interval_ms": 100, "adaptation_threshold": 0.5, "performance_window_size": 100}, "system": {"log_level": "INFO", "enable_metrics": true, "metrics_port": 8080, "max_workers": 4, "graceful_shutdown_timeout": 5.0}, "audio_backends": {"windows": {"preferred": "sounddevice", "fallback": "pya<PERSON>o"}, "linux": {"preferred": "pulse", "fallback": "alsa"}, "darwin": {"preferred": "<PERSON><PERSON><PERSON>", "fallback": "portaudio"}}}