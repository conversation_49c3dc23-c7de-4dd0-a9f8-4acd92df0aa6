"""
Decision engine for OODA loop adaptive behavior.
"""

import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, List, Optional, Callable
from ..utils.logging import LoggerMixin
from .controller import OODAState


class DecisionType(Enum):
    """Types of decisions the system can make."""
    # Audio-related decisions
    ADJUST_BUFFER_SIZE = "adjust_buffer_size"
    CHANGE_AUDIO_DEVICE = "change_audio_device"
    ADJUST_SAMPLE_RATE = "adjust_sample_rate"
    ENABLE_NOISE_GATE = "enable_noise_gate"
    
    # Model-related decisions
    SWITCH_MODEL = "switch_model"
    ADJUST_MODEL_PARAMS = "adjust_model_params"
    PRELOAD_MODEL = "preload_model"
    UNLOAD_MODEL = "unload_model"
    
    # Performance-related decisions
    ADJUST_CHUNK_SIZE = "adjust_chunk_size"
    THROTTLE_PROCESSING = "throttle_processing"
    INCREASE_WORKERS = "increase_workers"
    DECREASE_WORKERS = "decrease_workers"
    
    # System-related decisions
    RESTART_COMPONENT = "restart_component"
    ENABLE_FALLBACK = "enable_fallback"
    ADJUST_INTERVALS = "adjust_intervals"
    CLEAR_BUFFERS = "clear_buffers"


@dataclass
class Decision:
    """A decision made by the decision engine."""
    type: DecisionType
    priority: int  # 1-10, higher is more important
    parameters: Dict[str, Any]
    reason: str
    confidence: float  # 0.0-1.0
    timestamp: float
    expected_impact: str
    
    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


class DecisionEngine(LoggerMixin):
    """Core decision engine for adaptive system behavior."""
    
    def __init__(self, adaptation_threshold: float = 0.8):
        """Initialize decision engine.
        
        Args:
            adaptation_threshold: Threshold for triggering adaptations (0.0-1.0)
        """
        super().__init__()
        self.adaptation_threshold = adaptation_threshold
        self._decision_history: List[Decision] = []
        self._decision_rules: Dict[str, Callable] = {}
        self._max_history = 1000
        
        # Register default decision rules
        self._register_default_rules()
        
    def _register_default_rules(self):
        """Register default decision rules."""
        self.register_rule("audio_latency", self._decide_audio_latency)
        self.register_rule("buffer_management", self._decide_buffer_management)
        self.register_rule("model_performance", self._decide_model_performance)
        self.register_rule("system_resources", self._decide_system_resources)
        self.register_rule("transcription_quality", self._decide_transcription_quality)
    
    def register_rule(self, name: str, rule_func: Callable[[OODAState], List[Decision]]):
        """Register a decision rule.
        
        Args:
            name: Rule name
            rule_func: Function that takes OODAState and returns list of Decisions
        """
        self._decision_rules[name] = rule_func
        self.logger.debug("Registered decision rule", name=name)
    
    async def make_decisions(self, state: OODAState) -> List[Decision]:
        """Make decisions based on current state.
        
        Args:
            state: Current OODA state
            
        Returns:
            List of decisions to execute
        """
        all_decisions = []
        
        # Run all decision rules
        for rule_name, rule_func in self._decision_rules.items():
            try:
                decisions = rule_func(state)
                if decisions:
                    all_decisions.extend(decisions)
                    self.logger.debug("Rule generated decisions", 
                                    rule=rule_name, 
                                    count=len(decisions))
            except Exception as e:
                self.logger.error("Decision rule error", rule=rule_name, error=str(e))
        
        # Filter and prioritize decisions
        filtered_decisions = self._filter_decisions(all_decisions)
        prioritized_decisions = self._prioritize_decisions(filtered_decisions)
        
        # Store decisions in history
        self._decision_history.extend(prioritized_decisions)
        if len(self._decision_history) > self._max_history:
            self._decision_history = self._decision_history[-self._max_history:]
        
        self.logger.debug("Made decisions", 
                         total_generated=len(all_decisions),
                         filtered_count=len(prioritized_decisions))
        
        return prioritized_decisions
    
    def _decide_audio_latency(self, state: OODAState) -> List[Decision]:
        """Decide on audio latency optimizations."""
        decisions = []
        
        # Get audio observations
        audio_obs = state.observations.get("audio", {})
        if not audio_obs:
            return decisions
        
        latency_ms = audio_obs.get("latency_ms", 0)
        buffer_level = audio_obs.get("buffer_level", 0)
        
        # High latency decision
        if latency_ms > 300:  # Above 300ms is too high
            if buffer_level > 0.8:  # Buffer is full
                decisions.append(Decision(
                    type=DecisionType.ADJUST_BUFFER_SIZE,
                    priority=8,
                    parameters={"action": "decrease", "factor": 0.8},
                    reason=f"High latency ({latency_ms:.1f}ms) with full buffer",
                    confidence=0.9,
                    timestamp=time.time(),
                    expected_impact="Reduce latency by 20-30ms"
                ))
            else:
                decisions.append(Decision(
                    type=DecisionType.ADJUST_CHUNK_SIZE,
                    priority=7,
                    parameters={"action": "decrease", "factor": 0.8},
                    reason=f"High latency ({latency_ms:.1f}ms)",
                    confidence=0.8,
                    timestamp=time.time(),
                    expected_impact="Reduce processing latency"
                ))
        
        # Buffer overruns
        overruns = audio_obs.get("buffer_overruns", 0)
        if overruns > 10:  # Too many overruns
            decisions.append(Decision(
                type=DecisionType.ADJUST_BUFFER_SIZE,
                priority=6,
                parameters={"action": "increase", "factor": 1.2},
                reason=f"Too many buffer overruns ({overruns})",
                confidence=0.8,
                timestamp=time.time(),
                expected_impact="Reduce buffer overruns"
            ))
        
        return decisions
    
    def _decide_buffer_management(self, state: OODAState) -> List[Decision]:
        """Decide on buffer management optimizations."""
        decisions = []
        
        audio_obs = state.observations.get("audio", {})
        if not audio_obs:
            return decisions
        
        buffer_level = audio_obs.get("buffer_level", 0)
        
        # Buffer too full
        if buffer_level > 0.9:
            decisions.append(Decision(
                type=DecisionType.CLEAR_BUFFERS,
                priority=5,
                parameters={"target": "audio"},
                reason=f"Buffer nearly full ({buffer_level:.1%})",
                confidence=0.7,
                timestamp=time.time(),
                expected_impact="Free up buffer space"
            ))
        
        # Buffer consistently empty (might indicate problems)
        elif buffer_level < 0.1:
            audio_available = audio_obs.get("audio_available", False)
            if not audio_available:
                decisions.append(Decision(
                    type=DecisionType.RESTART_COMPONENT,
                    priority=9,
                    parameters={"component": "audio_capture"},
                    reason="Buffer empty and no audio available",
                    confidence=0.8,
                    timestamp=time.time(),
                    expected_impact="Restore audio capture"
                ))
        
        return decisions
    
    def _decide_model_performance(self, state: OODAState) -> List[Decision]:
        """Decide on model performance optimizations."""
        decisions = []
        
        model_obs = state.observations.get("model", {})
        system_obs = state.observations.get("system", {})
        
        if not model_obs:
            return decisions
        
        # Get performance metrics
        latency_stats = system_obs.get("latency_stats", {})
        transcription_stats = latency_stats.get("transcription", {})
        avg_latency = transcription_stats.get("avg_ms", 0)
        
        # High transcription latency
        if avg_latency > 500:  # Above 500ms
            decisions.append(Decision(
                type=DecisionType.SWITCH_MODEL,
                priority=7,
                parameters={"target": "faster", "reason": "high_latency"},
                reason=f"High transcription latency ({avg_latency:.1f}ms)",
                confidence=0.8,
                timestamp=time.time(),
                expected_impact="Reduce transcription latency"
            ))
        
        # Check if fallback model should be used
        accuracy = model_obs.get("accuracy_estimate", 0)
        if accuracy < 0.7 and avg_latency > 200:
            decisions.append(Decision(
                type=DecisionType.ENABLE_FALLBACK,
                priority=6,
                parameters={"component": "model", "reason": "low_accuracy_high_latency"},
                reason=f"Low accuracy ({accuracy:.1%}) and high latency",
                confidence=0.7,
                timestamp=time.time(),
                expected_impact="Use faster fallback model"
            ))
        
        return decisions
    
    def _decide_system_resources(self, state: OODAState) -> List[Decision]:
        """Decide on system resource optimizations."""
        decisions = []
        
        perf_obs = state.observations.get("performance", {})
        if not perf_obs:
            return decisions
        
        system_perf = perf_obs.get("system", {})
        process_perf = perf_obs.get("process", {})
        thresholds = perf_obs.get("thresholds", {})
        
        # High CPU usage
        if thresholds.get("cpu_high", False):
            cpu_percent = system_perf.get("cpu_percent", 0)
            decisions.append(Decision(
                type=DecisionType.THROTTLE_PROCESSING,
                priority=6,
                parameters={"factor": 0.8, "reason": "high_cpu"},
                reason=f"High CPU usage ({cpu_percent:.1f}%)",
                confidence=0.8,
                timestamp=time.time(),
                expected_impact="Reduce CPU usage"
            ))
        
        # High memory usage
        if thresholds.get("memory_high", False):
            memory_percent = system_perf.get("memory_percent", 0)
            decisions.append(Decision(
                type=DecisionType.UNLOAD_MODEL,
                priority=7,
                parameters={"target": "unused", "reason": "high_memory"},
                reason=f"High memory usage ({memory_percent:.1f}%)",
                confidence=0.7,
                timestamp=time.time(),
                expected_impact="Free memory"
            ))
        
        # Process memory too high
        if thresholds.get("process_memory_high", False):
            memory_mb = process_perf.get("memory_rss_mb", 0)
            decisions.append(Decision(
                type=DecisionType.CLEAR_BUFFERS,
                priority=5,
                parameters={"target": "all", "reason": "process_memory"},
                reason=f"High process memory ({memory_mb:.1f}MB)",
                confidence=0.6,
                timestamp=time.time(),
                expected_impact="Reduce process memory usage"
            ))
        
        return decisions
    
    def _decide_transcription_quality(self, state: OODAState) -> List[Decision]:
        """Decide on transcription quality optimizations."""
        decisions = []
        
        transcription_obs = state.observations.get("transcription", {})
        if not transcription_obs:
            return decisions
        
        accuracy = transcription_obs.get("accuracy_estimate", 0)
        error_rate = transcription_obs.get("error_rate", 0)
        
        # Low accuracy
        if accuracy < 0.8:
            decisions.append(Decision(
                type=DecisionType.SWITCH_MODEL,
                priority=5,
                parameters={"target": "higher_quality", "reason": "low_accuracy"},
                reason=f"Low transcription accuracy ({accuracy:.1%})",
                confidence=0.7,
                timestamp=time.time(),
                expected_impact="Improve transcription accuracy"
            ))
        
        # High error rate
        if error_rate > 0.1:
            decisions.append(Decision(
                type=DecisionType.RESTART_COMPONENT,
                priority=8,
                parameters={"component": "transcription_pipeline"},
                reason=f"High error rate ({error_rate:.1%})",
                confidence=0.8,
                timestamp=time.time(),
                expected_impact="Restore transcription pipeline stability"
            ))
        
        return decisions
    
    def _filter_decisions(self, decisions: List[Decision]) -> List[Decision]:
        """Filter out conflicting or low-confidence decisions.
        
        Args:
            decisions: List of decisions to filter
            
        Returns:
            Filtered list of decisions
        """
        if not decisions:
            return decisions
        
        # Filter by confidence threshold
        high_confidence = [d for d in decisions if d.confidence >= self.adaptation_threshold]
        
        # Remove conflicting decisions (keep highest priority)
        decision_map = {}
        for decision in high_confidence:
            key = decision.type
            if key not in decision_map or decision.priority > decision_map[key].priority:
                decision_map[key] = decision
        
        return list(decision_map.values())
    
    def _prioritize_decisions(self, decisions: List[Decision]) -> List[Decision]:
        """Sort decisions by priority.
        
        Args:
            decisions: List of decisions to prioritize
            
        Returns:
            Sorted list of decisions
        """
        return sorted(decisions, key=lambda d: d.priority, reverse=True)
    
    def get_decision_history(self, limit: int = 100) -> List[Decision]:
        """Get recent decision history.
        
        Args:
            limit: Maximum number of decisions to return
            
        Returns:
            List of recent decisions
        """
        return self._decision_history[-limit:] if limit > 0 else self._decision_history
    
    def get_decision_stats(self) -> Dict[str, Any]:
        """Get decision engine statistics.
        
        Returns:
            Dictionary of statistics
        """
        if not self._decision_history:
            return {"total_decisions": 0}
        
        # Count decisions by type
        type_counts = {}
        priority_sum = 0
        confidence_sum = 0
        
        recent_decisions = self._decision_history[-100:]  # Last 100 decisions
        
        for decision in recent_decisions:
            decision_type = decision.type.value
            type_counts[decision_type] = type_counts.get(decision_type, 0) + 1
            priority_sum += decision.priority
            confidence_sum += decision.confidence
        
        count = len(recent_decisions)
        
        return {
            "total_decisions": len(self._decision_history),
            "recent_decisions": count,
            "decision_types": type_counts,
            "avg_priority": priority_sum / count if count > 0 else 0,
            "avg_confidence": confidence_sum / count if count > 0 else 0,
            "adaptation_threshold": self.adaptation_threshold,
            "active_rules": len(self._decision_rules)
        }