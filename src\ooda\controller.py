"""
Core OODA loop controller implementation.
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Set
from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector


class OODAPhase(Enum):
    """OODA loop phases."""
    OBSERVE = "observe"
    ORIENT = "orient"
    DECIDE = "decide"
    ACT = "act"


@dataclass
class OODAState:
    """Current state of the OODA loop."""
    phase: OODAPhase
    cycle_count: int = 0
    observations: Dict[str, Any] = field(default_factory=dict)
    orientation: Dict[str, Any] = field(default_factory=dict)
    decisions: List[Any] = field(default_factory=list)
    actions_taken: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    last_cycle_time_ms: float = 0
    cycle_start_time: float = field(default_factory=time.time)


class OODAController(LoggerMixin):
    """OODA loop controller for adaptive system behavior."""
    
    def __init__(self, 
                 observe_interval_ms: int = 10,
                 orient_interval_ms: int = 50,
                 decide_interval_ms: int = 100,
                 act_interval_ms: int = 10):
        """Initialize OODA controller.
        
        Args:
            observe_interval_ms: Observation interval in milliseconds
            orient_interval_ms: Orientation interval in milliseconds
            decide_interval_ms: Decision interval in milliseconds
            act_interval_ms: Action interval in milliseconds
        """
        super().__init__()
        
        self.intervals = {
            OODAPhase.OBSERVE: observe_interval_ms / 1000.0,
            OODAPhase.ORIENT: orient_interval_ms / 1000.0,
            OODAPhase.DECIDE: decide_interval_ms / 1000.0,
            OODAPhase.ACT: act_interval_ms / 1000.0
        }
        
        # State
        self.state = OODAState(phase=OODAPhase.OBSERVE)
        self._running = False
        self._main_task: Optional[asyncio.Task] = None
        
        # Registered components
        self._observers: Dict[str, Callable] = {}
        self._orienters: Dict[str, Callable] = {}
        self._decision_engines: Dict[str, Callable] = {}
        self._actors: Dict[str, Callable] = {}
        
        # Performance tracking
        self._phase_times: Dict[OODAPhase, List[float]] = {
            phase: [] for phase in OODAPhase
        }
        self._enabled_phases: Set[OODAPhase] = set(OODAPhase)
        
        self.logger.info(
            "OODA controller initialized",
            intervals=self.intervals
        )
    
    def register_observer(self, name: str, observer: Callable[[OODAState], Dict[str, Any]]):
        """Register an observation function.
        
        Args:
            name: Observer name
            observer: Async function that takes OODAState and returns observations
        """
        self._observers[name] = observer
        self.logger.debug("Registered observer", name=name)
    
    def register_orienter(self, name: str, orienter: Callable[[OODAState], Dict[str, Any]]):
        """Register an orientation function.
        
        Args:
            name: Orienter name
            orienter: Async function that takes OODAState and returns orientation data
        """
        self._orienters[name] = orienter
        self.logger.debug("Registered orienter", name=name)
    
    def register_decision_engine(self, name: str, engine: Callable[[OODAState], List[Any]]):
        """Register a decision engine.
        
        Args:
            name: Engine name
            engine: Async function that takes OODAState and returns decisions
        """
        self._decision_engines[name] = engine
        self.logger.debug("Registered decision engine", name=name)
    
    def register_actor(self, name: str, actor: Callable[[OODAState], List[str]]):
        """Register an action executor.
        
        Args:
            name: Actor name
            actor: Async function that takes OODAState and returns action descriptions
        """
        self._actors[name] = actor
        self.logger.debug("Registered actor", name=name)
    
    def enable_phase(self, phase: OODAPhase):
        """Enable a specific OODA phase.
        
        Args:
            phase: Phase to enable
        """
        self._enabled_phases.add(phase)
        self.logger.debug("Enabled OODA phase", phase=phase.value)
    
    def disable_phase(self, phase: OODAPhase):
        """Disable a specific OODA phase.
        
        Args:
            phase: Phase to disable
        """
        self._enabled_phases.discard(phase)
        self.logger.debug("Disabled OODA phase", phase=phase.value)
    
    async def start(self):
        """Start the OODA loop."""
        if self._running:
            raise RuntimeError("OODA controller is already running")
            
        self._running = True
        self.state.cycle_count = 0
        
        self.logger.info("Starting OODA loop")
        
        self._main_task = asyncio.create_task(self._main_loop())
        
        try:
            await self._main_task
        except asyncio.CancelledError:
            self.logger.info("OODA loop cancelled")
        except Exception as e:
            self.logger.error("OODA loop error", error=str(e))
            raise
        finally:
            self._running = False
    
    async def stop(self):
        """Stop the OODA loop."""
        if not self._running:
            return
            
        self.logger.info("Stopping OODA loop")
        self._running = False
        
        if self._main_task:
            self._main_task.cancel()
            try:
                await self._main_task
            except asyncio.CancelledError:
                pass
            
        self.logger.info("OODA loop stopped")
    
    async def _main_loop(self):
        """Main OODA loop execution."""
        while self._running:
            cycle_start_time = time.time()
            self.state.cycle_start_time = cycle_start_time
            
            try:
                # Execute OODA phases in sequence
                if OODAPhase.OBSERVE in self._enabled_phases:
                    await self._observe_phase()
                    
                if OODAPhase.ORIENT in self._enabled_phases:
                    await self._orient_phase()
                    
                if OODAPhase.DECIDE in self._enabled_phases:
                    await self._decide_phase()
                    
                if OODAPhase.ACT in self._enabled_phases:
                    await self._act_phase()
                
                # Complete cycle
                cycle_end_time = time.time()
                cycle_duration_ms = (cycle_end_time - cycle_start_time) * 1000.0
                
                self.state.cycle_count += 1
                self.state.last_cycle_time_ms = cycle_duration_ms
                
                # Record performance metrics
                metrics_collector.record_latency("ooda_cycle", cycle_duration_ms)
                
                # Log performance periodically
                if self.state.cycle_count % 100 == 0:
                    self.logger.debug(
                        "OODA cycle stats",
                        cycle_count=self.state.cycle_count,
                        cycle_time_ms=cycle_duration_ms,
                        observations_count=len(self.state.observations),
                        decisions_count=len(self.state.decisions)
                    )
                
            except Exception as e:
                self.logger.error("Error in OODA cycle", error=str(e))
                metrics_collector.increment_counter(
                    "errors",
                    labels={"error_type": "ooda_cycle", "component": "controller"}
                )
                
                # Continue running unless critical error
                await asyncio.sleep(0.1)
    
    async def _observe_phase(self):
        """Execute the observe phase."""
        phase_start = time.time()
        self.state.phase = OODAPhase.OBSERVE
        
        try:
            # Clear previous observations
            self.state.observations.clear()
            
            # Run all observers
            for name, observer in self._observers.items():
                try:
                    observations = await observer(self.state)
                    if observations:
                        self.state.observations[name] = observations
                except Exception as e:
                    self.logger.error("Observer error", observer=name, error=str(e))
            
            # Wait for observe interval if needed
            elapsed = time.time() - phase_start
            remaining = self.intervals[OODAPhase.OBSERVE] - elapsed
            if remaining > 0:
                await asyncio.sleep(remaining)
                
        except Exception as e:
            self.logger.error("Observe phase error", error=str(e))
        finally:
            phase_duration = (time.time() - phase_start) * 1000.0
            self._phase_times[OODAPhase.OBSERVE].append(phase_duration)
            metrics_collector.record_latency("ooda_observe", phase_duration)
    
    async def _orient_phase(self):
        """Execute the orient phase."""
        phase_start = time.time()
        self.state.phase = OODAPhase.ORIENT
        
        try:
            # Clear previous orientation
            self.state.orientation.clear()
            
            # Run all orienters
            for name, orienter in self._orienters.items():
                try:
                    orientation = await orienter(self.state)
                    if orientation:
                        self.state.orientation[name] = orientation
                except Exception as e:
                    self.logger.error("Orienter error", orienter=name, error=str(e))
            
            # Wait for orient interval if needed
            elapsed = time.time() - phase_start
            remaining = self.intervals[OODAPhase.ORIENT] - elapsed
            if remaining > 0:
                await asyncio.sleep(remaining)
                
        except Exception as e:
            self.logger.error("Orient phase error", error=str(e))
        finally:
            phase_duration = (time.time() - phase_start) * 1000.0
            self._phase_times[OODAPhase.ORIENT].append(phase_duration)
            metrics_collector.record_latency("ooda_orient", phase_duration)
    
    async def _decide_phase(self):
        """Execute the decide phase."""
        phase_start = time.time()
        self.state.phase = OODAPhase.DECIDE
        
        try:
            # Clear previous decisions
            self.state.decisions.clear()
            
            # Run all decision engines
            for name, engine in self._decision_engines.items():
                try:
                    decisions = await engine(self.state)
                    if decisions:
                        self.state.decisions.extend(decisions)
                except Exception as e:
                    self.logger.error("Decision engine error", engine=name, error=str(e))
            
            # Wait for decide interval if needed
            elapsed = time.time() - phase_start
            remaining = self.intervals[OODAPhase.DECIDE] - elapsed
            if remaining > 0:
                await asyncio.sleep(remaining)
                
        except Exception as e:
            self.logger.error("Decide phase error", error=str(e))
        finally:
            phase_duration = (time.time() - phase_start) * 1000.0
            self._phase_times[OODAPhase.DECIDE].append(phase_duration)
            metrics_collector.record_latency("ooda_decide", phase_duration)
    
    async def _act_phase(self):
        """Execute the act phase."""
        phase_start = time.time()
        self.state.phase = OODAPhase.ACT
        
        try:
            # Clear previous actions
            self.state.actions_taken.clear()
            
            # Run all actors
            for name, actor in self._actors.items():
                try:
                    actions = await actor(self.state)
                    if actions:
                        self.state.actions_taken.extend(actions)
                except Exception as e:
                    self.logger.error("Actor error", actor=name, error=str(e))
            
            # Wait for act interval if needed
            elapsed = time.time() - phase_start
            remaining = self.intervals[OODAPhase.ACT] - elapsed
            if remaining > 0:
                await asyncio.sleep(remaining)
                
        except Exception as e:
            self.logger.error("Act phase error", error=str(e))
        finally:
            phase_duration = (time.time() - phase_start) * 1000.0
            self._phase_times[OODAPhase.ACT].append(phase_duration)
            metrics_collector.record_latency("ooda_act", phase_duration)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for the OODA loop.
        
        Returns:
            Dictionary of performance statistics
        """
        stats = {
            "cycle_count": self.state.cycle_count,
            "last_cycle_time_ms": self.state.last_cycle_time_ms,
            "is_running": self._running,
            "enabled_phases": [phase.value for phase in self._enabled_phases],
            "component_counts": {
                "observers": len(self._observers),
                "orienters": len(self._orienters),
                "decision_engines": len(self._decision_engines),
                "actors": len(self._actors)
            },
            "phase_stats": {}
        }
        
        # Calculate phase statistics
        for phase, times in self._phase_times.items():
            if times:
                recent_times = times[-100:]  # Last 100 measurements
                stats["phase_stats"][phase.value] = {
                    "count": len(times),
                    "avg_ms": sum(recent_times) / len(recent_times),
                    "min_ms": min(recent_times),
                    "max_ms": max(recent_times),
                    "target_interval_ms": self.intervals[phase] * 1000.0
                }
        
        return stats
    
    def update_intervals(self, **intervals):
        """Update phase intervals dynamically.
        
        Args:
            **intervals: Phase intervals in milliseconds (observe_ms, orient_ms, etc.)
        """
        for phase_name, interval_ms in intervals.items():
            if phase_name.endswith('_ms'):
                phase_name = phase_name[:-3]  # Remove '_ms' suffix
                
            try:
                phase = OODAPhase(phase_name)
                self.intervals[phase] = interval_ms / 1000.0
                self.logger.debug("Updated interval", phase=phase.value, interval_ms=interval_ms)
            except ValueError:
                self.logger.warning("Unknown phase for interval update", phase=phase_name)
    
    @property
    def is_running(self) -> bool:
        """Check if OODA loop is running."""
        return self._running