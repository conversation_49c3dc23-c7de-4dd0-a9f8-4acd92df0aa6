"""
High-level audio capture interface with automatic backend selection.
"""

import asyncio
import time
from dataclasses import dataclass
from typing import Optional, Callable, Any, Dict
import numpy as np

from ..utils.logging import LoggerMixin
from ..utils.metrics import metrics_collector
from ..streaming.ring_buffer import AudioRingBuffer
from .backends import get_audio_backend, AudioConfig
from .device import device_manager, AudioDevice


@dataclass
class AudioCapturerConfig:
    """Configuration for audio capturer."""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    buffer_duration_ms: int = 1000
    device_name: Optional[str] = None
    preferred_backend: Optional[str] = None
    enable_noise_gate: bool = True
    noise_gate_threshold: float = 0.01
    silence_timeout_ms: int = 2000


class AudioCapturer(LoggerMixin):
    """High-level audio capture with buffer management and processing."""
    
    def __init__(self, config: AudioCapturerConfig):
        """Initialize audio capturer.
        
        Args:
            config: Audio capturer configuration
        """
        super().__init__()
        self.config = config
        
        # Calculate buffer capacity in samples
        buffer_samples = int(
            (config.buffer_duration_ms / 1000.0) * config.sample_rate
        )
        
        # Create audio buffer
        self.audio_buffer = AudioRingBuffer(
            capacity_samples=buffer_samples,
            sample_rate=config.sample_rate,
            channels=config.channels,
            dtype=np.float32
        )
        
        # Backend and device
        self._backend = None
        self._device: Optional[AudioDevice] = None
        
        # State tracking
        self._is_capturing = False
        self._last_audio_time = 0
        self._audio_callbacks = []
        self._silence_start_time = None
        
        # Performance tracking
        self._callback_count = 0
        self._total_samples = 0
        self._start_time = 0
        
        self.logger.info(
            "Audio capturer initialized",
            sample_rate=config.sample_rate,
            channels=config.channels,
            chunk_size=config.chunk_size,
            buffer_duration_ms=config.buffer_duration_ms
        )
    
    async def start_capture(self) -> None:
        """Start audio capture.
        
        Raises:
            RuntimeError: If capture is already running or setup fails
        """
        if self._is_capturing:
            raise RuntimeError("Audio capture is already running")
            
        self.logger.info("Starting audio capture")
        
        try:
            # Select audio device
            await self._select_device()
            
            # Create backend configuration
            backend_config = AudioConfig(
                sample_rate=self.config.sample_rate,
                channels=self.config.channels,
                chunk_size=self.config.chunk_size,
                dtype=np.float32,
                device_id=self._device.extra_info.get('sd_index') if self._device else None
            )
            
            # Get audio backend
            self._backend = get_audio_backend(
                backend_config, 
                self.config.preferred_backend
            )
            
            # Start recording
            await self._backend.start_recording(self._audio_callback)
            
            self._is_capturing = True
            self._start_time = time.time()
            self._callback_count = 0
            self._total_samples = 0
            
            # Update metrics
            metrics_collector.set_gauge("active_streams", 1)
            
            self.logger.info(
                "Audio capture started",
                device=self._device.name if self._device else "default",
                backend=type(self._backend).__name__,
                latency_ms=self._backend.get_latency_ms()
            )
            
        except Exception as e:
            self.logger.error("Failed to start audio capture", error=str(e))
            await self._cleanup()
            raise
    
    async def stop_capture(self) -> None:
        """Stop audio capture."""
        if not self._is_capturing:
            return
            
        self.logger.info("Stopping audio capture")
        
        try:
            if self._backend:
                await self._backend.stop_recording()
                
            self._is_capturing = False
            
            # Update metrics
            metrics_collector.set_gauge("active_streams", 0)
            
            # Log performance stats
            duration = time.time() - self._start_time
            if duration > 0:
                avg_callback_rate = self._callback_count / duration
                avg_sample_rate = self._total_samples / duration
                
                self.logger.info(
                    "Audio capture stopped",
                    duration_seconds=duration,
                    total_callbacks=self._callback_count,
                    total_samples=self._total_samples,
                    avg_callback_rate_hz=avg_callback_rate,
                    avg_sample_rate_hz=avg_sample_rate
                )
                
        except Exception as e:
            self.logger.error("Error stopping audio capture", error=str(e))
        finally:
            await self._cleanup()
    
    async def _cleanup(self):
        """Clean up resources."""
        self._backend = None
        self._device = None
        self._is_capturing = False
        self.audio_buffer.clear()
    
    async def _select_device(self):
        """Select audio input device."""
        if self.config.device_name:
            # Find specific device
            self._device = device_manager.find_device(
                self.config.device_name, 
                input_device=True
            )
            if not self._device:
                raise RuntimeError(f"Audio device not found: {self.config.device_name}")
        else:
            # Use default device
            self._device = device_manager.get_default_input_device()
            if not self._device:
                raise RuntimeError("No default input device available")
        
        self.logger.info(
            "Selected audio device",
            name=self._device.name,
            channels=self._device.max_input_channels,
            sample_rate=self._device.default_sample_rate,
            backend=self._device.backend
        )
    
    async def _audio_callback(self, audio_data: np.ndarray, timestamp: float):
        """Handle audio data from backend.
        
        Args:
            audio_data: Audio samples
            timestamp: Capture timestamp
        """
        try:
            with metrics_collector.measure_latency("audio_processing"):
                self._callback_count += 1
                self._total_samples += len(audio_data)
                self._last_audio_time = timestamp
                
                # Apply noise gate if enabled
                if self.config.enable_noise_gate:
                    audio_data = self._apply_noise_gate(audio_data, timestamp)
                
                # Write to buffer
                samples_written = self.audio_buffer.write_audio(audio_data)
                
                if samples_written < len(audio_data):
                    self.logger.warning(
                        "Audio buffer full, dropping samples",
                        requested=len(audio_data),
                        written=samples_written
                    )
                    metrics_collector.increment_counter("buffer_overruns")
                
                # Call registered callbacks
                for callback in self._audio_callbacks:
                    try:
                        await callback(audio_data, timestamp)
                    except Exception as e:
                        self.logger.error("Error in audio callback", error=str(e))
                        
        except Exception as e:
            self.logger.error("Error in audio processing", error=str(e))
            metrics_collector.increment_counter(
                "errors", 
                labels={"error_type": "audio_callback", "component": "capturer"}
            )
    
    def _apply_noise_gate(self, audio_data: np.ndarray, timestamp: float) -> np.ndarray:
        """Apply noise gate to audio data.
        
        Args:
            audio_data: Input audio samples
            timestamp: Capture timestamp
            
        Returns:
            Processed audio samples
        """
        # Calculate RMS level
        rms = np.sqrt(np.mean(audio_data ** 2))
        
        if rms < self.config.noise_gate_threshold:
            # Below threshold - start/continue silence
            if self._silence_start_time is None:
                self._silence_start_time = timestamp
            
            # Check if silence timeout exceeded
            silence_duration = (timestamp - self._silence_start_time) * 1000
            if silence_duration > self.config.silence_timeout_ms:
                # Return silence
                return np.zeros_like(audio_data)
        else:
            # Above threshold - reset silence timer
            self._silence_start_time = None
        
        return audio_data
    
    def add_audio_callback(self, callback: Callable[[np.ndarray, float], Any]):
        """Add a callback for audio data.
        
        Args:
            callback: Async function to call with (audio_data, timestamp)
        """
        self._audio_callbacks.append(callback)
        self.logger.debug("Added audio callback", total_callbacks=len(self._audio_callbacks))
    
    def remove_audio_callback(self, callback: Callable[[np.ndarray, float], Any]):
        """Remove an audio callback.
        
        Args:
            callback: Callback function to remove
        """
        if callback in self._audio_callbacks:
            self._audio_callbacks.remove(callback)
            self.logger.debug("Removed audio callback", total_callbacks=len(self._audio_callbacks))
    
    def get_audio_data(self, duration_ms: float, timeout: float = 1.0) -> Optional[np.ndarray]:
        """Get audio data of specific duration.
        
        Args:
            duration_ms: Duration to get in milliseconds
            timeout: Timeout in seconds
            
        Returns:
            Audio data or None if timeout
        """
        try:
            return self.audio_buffer.read_duration(duration_ms, timeout)
        except TimeoutError:
            return None
    
    def has_audio_data(self, min_duration_ms: float) -> bool:
        """Check if buffer has minimum audio duration.
        
        Args:
            min_duration_ms: Minimum duration in milliseconds
            
        Returns:
            True if buffer has enough audio
        """
        return self.audio_buffer.has_minimum_audio(min_duration_ms)
    
    def get_latency_ms(self) -> float:
        """Get current audio latency in milliseconds.
        
        Returns:
            Latency in milliseconds
        """
        backend_latency = self._backend.get_latency_ms() if self._backend else 0
        buffer_latency = self.audio_buffer.get_duration_ms()
        return backend_latency + buffer_latency
    
    def get_capture_stats(self) -> Dict[str, Any]:
        """Get capture statistics.
        
        Returns:
            Dictionary of capture statistics
        """
        buffer_stats = self.audio_buffer.get_stats()
        
        duration = time.time() - self._start_time if self._start_time > 0 else 0
        
        return {
            "is_capturing": self._is_capturing,
            "device_name": self._device.name if self._device else None,
            "backend": type(self._backend).__name__ if self._backend else None,
            "sample_rate": self.config.sample_rate,
            "channels": self.config.channels,
            "buffer_stats": buffer_stats.__dict__,
            "callback_count": self._callback_count,
            "total_samples": self._total_samples,
            "capture_duration_s": duration,
            "avg_callback_rate_hz": self._callback_count / duration if duration > 0 else 0,
            "current_latency_ms": self.get_latency_ms(),
            "last_audio_time": self._last_audio_time
        }
    
    @property
    def is_capturing(self) -> bool:
        """Check if currently capturing audio."""
        return self._is_capturing
    
    @property
    def device(self) -> Optional[AudioDevice]:
        """Get current audio device."""
        return self._device