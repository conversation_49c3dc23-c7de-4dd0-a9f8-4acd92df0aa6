"""
Audio device detection and management.
"""

import platform
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from ..utils.logging import LoggerMixin


@dataclass
class AudioDevice:
    """Audio device information."""
    id: str
    name: str
    max_input_channels: int
    max_output_channels: int
    default_sample_rate: float
    supported_sample_rates: List[float]
    is_default_input: bool = False
    is_default_output: bool = False
    backend: str = "unknown"
    extra_info: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.extra_info is None:
            self.extra_info = {}


class AudioDeviceManager(LoggerMixin):
    """Cross-platform audio device detection and management."""
    
    def __init__(self):
        """Initialize audio device manager."""
        super().__init__()
        self._devices: Optional[List[AudioDevice]] = None
        self._default_input: Optional[AudioDevice] = None
        self._default_output: Optional[AudioDevice] = None
        
    def scan_devices(self, force_rescan: bool = False) -> List[AudioDevice]:
        """Scan for available audio devices.
        
        Args:
            force_rescan: Force rescan even if devices are cached
            
        Returns:
            List of available audio devices
        """
        if self._devices is not None and not force_rescan:
            return self._devices
            
        self.logger.info("Scanning audio devices")
        
        devices = []
        
        # Try different backends in order of preference
        try:
            # Try sounddevice first (cross-platform)
            devices.extend(self._scan_sounddevice())
        except Exception as e:
            self.logger.warning("Failed to scan sounddevice", error=str(e))
            
        try:
            # Try PyAudio as fallback
            devices.extend(self._scan_pyaudio())
        except Exception as e:
            self.logger.warning("Failed to scan PyAudio", error=str(e))
            
        # Platform-specific scanning
        system = platform.system().lower()
        
        if system == "windows":
            try:
                devices.extend(self._scan_windows_devices())
            except Exception as e:
                self.logger.warning("Failed to scan Windows devices", error=str(e))
                
        elif system == "linux":
            try:
                devices.extend(self._scan_linux_devices())
            except Exception as e:
                self.logger.warning("Failed to scan Linux devices", error=str(e))
                
        elif system == "darwin":
            try:
                devices.extend(self._scan_macos_devices())
            except Exception as e:
                self.logger.warning("Failed to scan macOS devices", error=str(e))
        
        # Remove duplicates and set defaults
        devices = self._deduplicate_devices(devices)
        self._set_default_devices(devices)
        
        self._devices = devices
        self.logger.info("Found audio devices", count=len(devices))
        
        return devices
    
    def _scan_sounddevice(self) -> List[AudioDevice]:
        """Scan devices using sounddevice."""
        try:
            import sounddevice as sd
            
            devices = []
            device_list = sd.query_devices()
            
            for i, device_info in enumerate(device_list):
                device = AudioDevice(
                    id=f"sounddevice_{i}",
                    name=device_info['name'],
                    max_input_channels=device_info['max_input_channels'],
                    max_output_channels=device_info['max_output_channels'],
                    default_sample_rate=device_info['default_samplerate'],
                    supported_sample_rates=self._get_supported_sample_rates_sd(i),
                    backend="sounddevice",
                    extra_info={
                        "sd_index": i,
                        "hostapi": device_info.get('hostapi', 0)
                    }
                )
                devices.append(device)
                
            return devices
            
        except ImportError:
            return []
    
    def _scan_pyaudio(self) -> List[AudioDevice]:
        """Scan devices using PyAudio."""
        try:
            import pyaudio
            
            devices = []
            pa = pyaudio.PyAudio()
            
            try:
                for i in range(pa.get_device_count()):
                    device_info = pa.get_device_info_by_index(i)
                    
                    device = AudioDevice(
                        id=f"pyaudio_{i}",
                        name=device_info['name'],
                        max_input_channels=device_info['maxInputChannels'],
                        max_output_channels=device_info['maxOutputChannels'],
                        default_sample_rate=device_info['defaultSampleRate'],
                        supported_sample_rates=self._get_supported_sample_rates_pa(pa, i),
                        backend="pyaudio",
                        extra_info={
                            "pa_index": i,
                            "hostapi": device_info.get('hostApi', 0)
                        }
                    )
                    devices.append(device)
                    
            finally:
                pa.terminate()
                
            return devices
            
        except ImportError:
            return []
    
    def _scan_windows_devices(self) -> List[AudioDevice]:
        """Scan Windows-specific audio devices."""
        devices = []
        
        try:
            # Try to use Windows Core Audio APIs
            import ctypes
            from ctypes import wintypes
            
            # This is a simplified version - full implementation would use
            # Windows Core Audio APIs to enumerate WASAPI devices
            pass
            
        except (ImportError, OSError):
            pass
            
        return devices
    
    def _scan_linux_devices(self) -> List[AudioDevice]:
        """Scan Linux-specific audio devices."""
        devices = []
        
        try:
            # Try PulseAudio
            import pulsectl
            
            with pulsectl.Pulse('device_scanner') as pulse:
                # Get source (input) devices
                for source in pulse.source_list():
                    if not source.name.endswith('.monitor'):  # Skip monitor sources
                        device = AudioDevice(
                            id=f"pulse_source_{source.index}",
                            name=source.description or source.name,
                            max_input_channels=source.channel_count,
                            max_output_channels=0,
                            default_sample_rate=float(source.sample_spec.rate),
                            supported_sample_rates=[8000, 16000, 22050, 44100, 48000],
                            backend="pulse",
                            extra_info={
                                "pulse_index": source.index,
                                "pulse_name": source.name,
                                "card_index": getattr(source, 'card', None)
                            }
                        )
                        devices.append(device)
                        
        except ImportError:
            pass
            
        try:
            # Try ALSA
            import alsaaudio
            
            # Get capture devices
            pcm_list = alsaaudio.pcms(alsaaudio.PCM_CAPTURE)
            for i, pcm_name in enumerate(pcm_list):
                device = AudioDevice(
                    id=f"alsa_capture_{i}",
                    name=pcm_name,
                    max_input_channels=2,  # Conservative default
                    max_output_channels=0,
                    default_sample_rate=44100.0,
                    supported_sample_rates=[8000, 16000, 22050, 44100, 48000],
                    backend="alsa",
                    extra_info={
                        "alsa_name": pcm_name
                    }
                )
                devices.append(device)
                
        except ImportError:
            pass
            
        return devices
    
    def _scan_macos_devices(self) -> List[AudioDevice]:
        """Scan macOS-specific audio devices."""
        devices = []
        
        # macOS CoreAudio scanning would go here
        # For now, rely on sounddevice/PyAudio
        
        return devices
    
    def _get_supported_sample_rates_sd(self, device_index: int) -> List[float]:
        """Get supported sample rates for sounddevice device."""
        try:
            import sounddevice as sd
            
            common_rates = [8000, 16000, 22050, 44100, 48000, 88200, 96000]
            supported = []
            
            for rate in common_rates:
                try:
                    sd.check_input_settings(device=device_index, samplerate=rate)
                    supported.append(float(rate))
                except:
                    pass
                    
            return supported if supported else [44100.0]
            
        except ImportError:
            return [44100.0]
    
    def _get_supported_sample_rates_pa(self, pa, device_index: int) -> List[float]:
        """Get supported sample rates for PyAudio device."""
        try:
            import pyaudio
            
            common_rates = [8000, 16000, 22050, 44100, 48000, 88200, 96000]
            supported = []
            
            device_info = pa.get_device_info_by_index(device_index)
            
            for rate in common_rates:
                try:
                    if pa.is_format_supported(
                        rate=rate,
                        input_device=device_index,
                        input_channels=1,
                        input_format=pyaudio.paFloat32
                    ):
                        supported.append(float(rate))
                except:
                    pass
                    
            return supported if supported else [44100.0]
            
        except:
            return [44100.0]
    
    def _deduplicate_devices(self, devices: List[AudioDevice]) -> List[AudioDevice]:
        """Remove duplicate devices based on name and capabilities.
        
        Args:
            devices: List of devices to deduplicate
            
        Returns:
            Deduplicated list of devices
        """
        seen = set()
        unique_devices = []
        
        for device in devices:
            # Create key based on name and input channels
            key = (device.name.lower().strip(), device.max_input_channels)
            
            if key not in seen:
                seen.add(key)
                unique_devices.append(device)
                
        return unique_devices
    
    def _set_default_devices(self, devices: List[AudioDevice]):
        """Set default input and output devices.
        
        Args:
            devices: List of devices to analyze
        """
        input_devices = [d for d in devices if d.max_input_channels > 0]
        output_devices = [d for d in devices if d.max_output_channels > 0]
        
        # Try to find system default
        self._default_input = None
        self._default_output = None
        
        # Look for devices marked as default
        for device in input_devices:
            if device.is_default_input:
                self._default_input = device
                break
                
        for device in output_devices:
            if device.is_default_output:
                self._default_output = device
                break
                
        # Fallback to first available device
        if self._default_input is None and input_devices:
            self._default_input = input_devices[0]
            self._default_input.is_default_input = True
            
        if self._default_output is None and output_devices:
            self._default_output = output_devices[0]
            self._default_output.is_default_output = True
    
    def get_default_input_device(self) -> Optional[AudioDevice]:
        """Get the default input device.
        
        Returns:
            Default input device or None
        """
        if self._devices is None:
            self.scan_devices()
            
        return self._default_input
    
    def get_default_output_device(self) -> Optional[AudioDevice]:
        """Get the default output device.
        
        Returns:
            Default output device or None
        """
        if self._devices is None:
            self.scan_devices()
            
        return self._default_output
    
    def get_input_devices(self) -> List[AudioDevice]:
        """Get all input devices.
        
        Returns:
            List of input devices
        """
        if self._devices is None:
            self.scan_devices()
            
        return [d for d in self._devices if d.max_input_channels > 0]
    
    def get_output_devices(self) -> List[AudioDevice]:
        """Get all output devices.
        
        Returns:
            List of output devices
        """
        if self._devices is None:
            self.scan_devices()
            
        return [d for d in self._devices if d.max_output_channels > 0]
    
    def find_device(self, name: str, input_device: bool = True) -> Optional[AudioDevice]:
        """Find device by name.
        
        Args:
            name: Device name to search for
            input_device: Whether to look for input device
            
        Returns:
            Found device or None
        """
        if self._devices is None:
            self.scan_devices()
            
        name_lower = name.lower()
        devices = self.get_input_devices() if input_device else self.get_output_devices()
        
        # Exact match first
        for device in devices:
            if device.name.lower() == name_lower:
                return device
                
        # Partial match
        for device in devices:
            if name_lower in device.name.lower():
                return device
                
        return None


# Global device manager instance
device_manager = AudioDeviceManager()